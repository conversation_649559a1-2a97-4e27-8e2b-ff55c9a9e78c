using Microsoft.Playwright;
using RealEstateScraperApi.Interfaces;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace RealEstateVisningApp.LLMProcessing.BrokerHandlers;

public class DefaultBrokerHandler : IBrokerScrapingHandler
{
    private readonly ILogger<DefaultBrokerHandler> _logger;
    // More specific content selectors first, broader ones last
    private static readonly string[] ContentSelectors = { "article", "main", ".content", ".main-content", "body" };

    public DefaultBrokerHandler(ILogger<DefaultBrokerHandler> logger)
    {
        _logger = logger;
    }

    // This handler doesn't target a specific hostname, it's the fallback.
    public string HandlerHostname => "default";

    public async Task<string?> ScrapeProspectusAsync(IPage page, string brokerUrl)
    {
        _logger.LogInformation("Attempting default scrape for URL: {BrokerUrl}", brokerUrl);

        try
        {
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle, new PageWaitForLoadStateOptions { Timeout = 15000 }); // Wait for dynamic content

            foreach (var selector in ContentSelectors)
            {
                try
                {
                    var element = page.Locator(selector).First; // Try finding the first matching element
                    if (await element.IsVisibleAsync(new LocatorIsVisibleOptions { Timeout = 2000 })) // Check if reasonably visible quickly
                    {
                        string? content = await element.InnerTextAsync(new LocatorInnerTextOptions { Timeout = 5000 });
                        if (!string.IsNullOrWhiteSpace(content))
                        {
                            _logger.LogInformation("Successfully extracted content using default selector '{Selector}' for {BrokerUrl}", selector, brokerUrl);
                            // Basic cleanup (can be improved)
                            content = System.Text.RegularExpressions.Regex.Replace(content, @"\s{2,}", " ").Trim();
                            return content;
                        }
                    }
                }
                catch (TimeoutException)
                {
                     _logger.LogDebug("Selector '{Selector}' timed out or not found for {BrokerUrl}", selector, brokerUrl);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error checking default selector '{Selector}' for {BrokerUrl}", selector, brokerUrl);
                }
            }

             _logger.LogWarning("Default handler could not extract significant content from {BrokerUrl} using selectors: {Selectors}", brokerUrl, string.Join(", ", ContentSelectors));
            return null; // Indicate failure if no selector worked

        }
        catch (TimeoutException tex)
        {
            _logger.LogError(tex, "Timeout waiting for page load/network idle on {BrokerUrl} in DefaultBrokerHandler", brokerUrl);
            throw new ScrapingException($"Timeout loading page {brokerUrl} for default scraping.", tex);
        }
        catch (PlaywrightException pex)
        {
             _logger.LogError(pex, "Playwright error during default scrape for {BrokerUrl}", brokerUrl);
            throw new ScrapingException($"Playwright error during default scraping for {brokerUrl}.", pex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during default scrape for {BrokerUrl}", brokerUrl);
            throw new ScrapingException($"Unexpected error during default scraping for {brokerUrl}.", ex);
        }
    }
}