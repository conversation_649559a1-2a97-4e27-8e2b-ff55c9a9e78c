using RealEstateScraperApi.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;

namespace RealEstateVisningApp.LLMProcessing.BrokerHandlers;

public class BrokerHandlerFactory : IBrokerHandlerFactory
{
    private readonly Dictionary<string, IBrokerScrapingHandler> _handlers;
    private readonly IBrokerScrapingHandler _defaultHandler;
    private readonly ILogger<BrokerHandlerFactory> _logger;

    // Inject all registered handlers and the specific default handler
    public BrokerHandlerFactory(IEnumerable<IBrokerScrapingHandler> handlers,
                                 DefaultBrokerHandler defaultHandler, // Specifically request the default one
                                 ILogger<BrokerHandlerFactory> logger)
    {
        _logger = logger;
        // Create a dictionary mapping hostname to handler, excluding the default handler itself
        _handlers = handlers.Where(h => h.GetType() != typeof(BrokerHandlers.DefaultBrokerHandler))
                            .ToDictionary(h => h.HandlerHostname.ToLowerInvariant(), h => h);
        
        
        _defaultHandler = defaultHandler;

         _logger.LogInformation("BrokerHandlerFactory initialized with {Count} specific handlers: {Handlers}",
             _handlers.Count, string.Join(", ", _handlers.Keys));
    }

    public IBrokerScrapingHandler GetHandler(Uri brokerUri)
    {
        ArgumentNullException.ThrowIfNull(brokerUri);

        var host = brokerUri.Host.ToLowerInvariant();

        // Handle common variations like "www."
        if (host.StartsWith("www."))
        {
            host = host.Substring(4);
        }

        if (_handlers.TryGetValue(host, out var handler))
        {
            _logger.LogDebug("Found specific handler for host {Host}: {HandlerType}", host, handler.GetType().Name);
            return handler;
        }

        _logger.LogDebug("No specific handler found for host {Host}. Using DefaultBrokerHandler.", host);
        return _defaultHandler;
    }
}