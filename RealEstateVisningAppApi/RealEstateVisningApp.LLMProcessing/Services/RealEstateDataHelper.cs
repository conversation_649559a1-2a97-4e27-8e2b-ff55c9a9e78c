using Microsoft.EntityFrameworkCore;
using RealEstateVisningApp.Database.Repository;

namespace RealEstateVisningApp.LLMProcessing.Services;

public class RealEstateDataHelper
{
    private readonly IRealEstateRepository _realEstateRepository;
    private readonly IFinnScraperService _finnScraperService;

    public RealEstateDataHelper(IRealEstateRepository realEstateRepository, IFinnScraperService finnScraperService)
    {
        _finnScraperService = finnScraperService;
        _realEstateRepository = realEstateRepository;
    }   
    
    public async Task SetSalgsoppgaveUrlOnAllRealEstates()
    {
        var allRealEstates = await _realEstateRepository.GetQueryable().Where(a=> a.SalgsoppgaveUrl == null).ToListAsync();
        
        foreach (var realEstate in allRealEstates)
        {
            try
            {
                string? url = "";
                try
                {
                    url = await _finnScraperService.FindProspectusUrlAsync(realEstate.FinnAdId.ToString());
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
               
                realEstate.SalgsoppgaveUrl = url ?? "";
                await _realEstateRepository.UpdateAsync(realEstate);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            await Task.Delay(100);
        }
    }
}