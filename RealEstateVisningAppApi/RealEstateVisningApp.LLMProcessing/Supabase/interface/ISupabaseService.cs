using System.Threading.Tasks;
using RealEstateScraperApi.Interfaces; // For ProcessedLlmData

namespace RealEstateVisningApp.LLMProcessing;

// Define a model that represents your Supabase table structure
public class RealEstateListingRecord
{
    // Supabase typically uses snake_case, map accordingly or use attributes
    // [Column("finn_kode")]
    public string FinnKode { get; set; } = string.Empty;

    // [Column("summary")]
    public string? Summary { get; set; }

    // [Column("details")] // Example: Storing details as JSONB
    public Dictionary<string, string>? Details { get; set; }

    // [Column("scraped_at")]
    public DateTime ScrapedAt { get; set; }

    // Add other relevant fields: Address, Price, Area, etc. extracted by LLM
    // [Column("address")]
    public string? Address {get; set;}
    // [Column("price")]
    public long? Price { get; set; } // Use appropriate type
}


public interface ISupabaseService
{
    Task<bool> SaveListingAsync(string finnKode, string processedData);
}