using RealEstateScraperApi.Interfaces; // For ProcessedLlmData

namespace RealEstateVisningApp.LLMProcessing;
public class SaveListingRequest
{
    [System.ComponentModel.DataAnnotations.Required]
    [System.ComponentModel.DataAnnotations.RegularExpression("^[0-9]+$", ErrorMessage = "FinnKode must be numeric.")]
    public string FinnKode { get; set; } = string.Empty;

    [System.ComponentModel.DataAnnotations.Required]
    public string? ProcessedData { get; set; }
}

public class SaveListingResponse
{
    public string FinnKode { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string? Message { get; set; }
}