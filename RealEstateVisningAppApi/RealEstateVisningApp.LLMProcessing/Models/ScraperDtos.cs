namespace RealEstateVisningApp.LLMProcessing;
public class GetProspectusUrlRequest
{
    [System.ComponentModel.DataAnnotations.Required]
    [System.ComponentModel.DataAnnotations.RegularExpression("^[0-9]+$", ErrorMessage = "FinnKode must be numeric.")]
    public string FinnKode { get; set; } = string.Empty;
}

public class ScrapeProspectusContentRequest
{
    [System.ComponentModel.DataAnnotations.Required]
    [System.ComponentModel.DataAnnotations.Url]
    public string ProspectusUrl { get; set; } = string.Empty;

    // Include FinnKode for context in case of errors/notifications
    [System.ComponentModel.DataAnnotations.Required]
     [System.ComponentModel.DataAnnotations.RegularExpression("^[0-9]+$", ErrorMessage = "FinnKode must be numeric.")]
    public string FinnKode { get; set; } = string.Empty;
}

public class ScrapedDataResponse
{
     public string FinnKode { get; set; } = string.Empty;
     public string BrokerUrl { get; set; } = string.Empty;
     public string? Content { get; set; } // Null if scraping failed
     public bool Success { get; set; }
     public string? ErrorMessage { get; set; }
}

public class ProspectusUrlResponse
{
    public string FinnKode { get; set; } = string.Empty;
    public string? ProspectusUrl { get; set; } // Null if not found
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
}