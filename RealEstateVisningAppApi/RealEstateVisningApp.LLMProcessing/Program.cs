using System.Reflection;
using Microsoft.EntityFrameworkCore;
using Microsoft.Playwright;
using RealEstateScraperApi.Interfaces;
using RealEstateVisningApp.Database;
using RealEstateVisningApp.Database.Repository;
using RealEstateVisningApp.LLMProcessing.BrokerHandlers;
using RealEstateVisningApp.LLMProcessing.Services;

namespace RealEstateVisningApp.LLMProcessing;

public class Program
{
    public static void Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);

        // Add services to the container.

        builder.Services.AddControllers();
        // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
        builder.Services.AddEndpointsApiExplorer();
        builder.Services.AddSwaggerGen();

        // Playwright (Singleton for the playwright instance itself)
        builder.Services.AddSingleton<IPlaywright>(sp => Playwright.CreateAsync().GetAwaiter().GetResult());
        
        // Scraper Services
        builder.Services.AddScoped<IFinnScraperService, FinnScraperService>();

        // Broker Handlers (Register all specific handlers and the default one)
        builder.Services.AddBrokerScrapingHandlers(Assembly.GetExecutingAssembly());
        // Register default handler separately
        builder.Services.AddScoped<DefaultBrokerHandler>(); // NOT as IBrokerScrapingHandler

        
        // Broker Handler Factory (Scoped, depends on IBrokerScrapingHandler instances)
        builder.Services.AddScoped<IBrokerHandlerFactory, BrokerHandlerFactory>();
        
        // Notification Service
        builder.Services.AddScoped<INotificationService, SentryNotificationService>(); // Uses Sentry Hub

        // LLM Service (Placeholder)
        builder.Services.AddHttpClient("LlmApiClient", client =>
        {
            // Configure base address, default headers, timeouts etc. for your LLM API
            // client.BaseAddress = new Uri(configuration["LlmApi:BaseUrl"]);
            // client.DefaultRequestHeaders.Add("Authorization", $"Bearer {configuration["LlmApi:ApiKey"]}");
        });
        
        var configuration = builder.Configuration;
        // --- Logging ---
        builder.Logging.ClearProviders();
        builder.Logging.AddConsole();
        
        // --- Sentry Integration ---
        builder.WebHost.UseSentry(o =>
        {
            o.Dsn = configuration["Sentry:Dsn"]; // Get DSN from config (appsettings.json or env vars)
            o.Debug = builder.Environment.IsDevelopment();
            o.TracesSampleRate = 1.0; // Adjust as needed
            o.SendDefaultPii = true; // Be careful with PII
            // Configure more options if needed
        });
        
        builder.Services.AddDbContext<RealEstateDbContext>(options =>
            options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));
        
        builder.Services.AddScoped<IRealEstateRepository, RealEstateRepository>();   
        builder.Services.AddScoped<ILlmService, LLMService>();
        // builder.Services.AddScoped<FinnRealEstateService>();   
        
        var app = builder.Build();

        // Configure the HTTP request pipeline.
        if (app.Environment.IsDevelopment())
        {
            app.UseSwagger();
            app.UseSwaggerUI();
        }

        app.UseHttpsRedirection();

        app.UseAuthorization();


        app.MapControllers();

        app.Run();
    }
}