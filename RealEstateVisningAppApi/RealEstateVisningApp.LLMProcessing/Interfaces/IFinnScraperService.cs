using System.Threading.Tasks;

namespace RealEstateVisningApp.LLMProcessing;
public record ScrapedContentResult(string FinnKode, string BrokerUrl, string? Content);

public interface IFinnScraperService
{
    /// <summary>
    /// Finds the URL for the "Se komplett salgsoppgave" link on a Finn.no ad page.
    /// </summary>
    /// <param name="finnKode">The Finn Kode.</param>
    /// <returns>The URL string or null if not found.</returns>
    Task<string?> FindProspectusUrlAsync(string finnKode);

    /// <summary>
    /// Scrapes the content from the broker URL obtained from <PERSON>.
    /// </summary>
    /// <param name="finnKode">The Finn Kode (for context/logging).</param>
    /// <param name="brokerUrl">The URL found by FindProspectusUrlAsync.</param>
    /// <returns>A result object containing the scraped content, or null content if scraping failed.</returns>
    Task<ScrapedContentResult> ScrapeBrokerContentAsync(string finnKode, string brokerUrl);
}