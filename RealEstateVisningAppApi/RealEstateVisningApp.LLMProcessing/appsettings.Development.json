{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Supabase": {"Url": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBncndrbm91eWZoemhtd3Z6YWdoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjI4NzA2MDQsImV4cCI6MjAzODQ0NjYwNH0.zs6rnaNyGZpfCloDLgVH6UHYnXzS3DDGoLN8SQrx9sU", "ApiKey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBncndrbm91eWZoemhtd3Z6YWdoIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyMjg3MDYwNCwiZXhwIjoyMDM4NDQ2NjA0fQ.xReqEBatxNcHmIJQVj1TnJkJeveNFa3mjGbAz98HqW8"}, "Sentry": {"Dsn": "https://<EMAIL>/4509062948388944"}, "ConnectionStrings": {"DefaultConnection": "User Id=postgres.pgrwknouyfhzhmwvzagh;Password=*************;Server=aws-0-eu-central-1.pooler.supabase.com;Port=5432;Database=postgres;"}, "GoogleAI": {"ApiKey": "AIzaSyDViyjARUZc8hbBWXnwMKcEtBzcXxAHFco", "ProjectId": "your-project-id", "LocationId": "your-location-id"}}