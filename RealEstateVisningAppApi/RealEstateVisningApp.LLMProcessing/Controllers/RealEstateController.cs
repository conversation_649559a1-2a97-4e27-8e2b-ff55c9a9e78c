using Microsoft.AspNetCore.Mvc;
using RealEstateScraperApi.Interfaces;
using RealEstateVisningApp.Database.Repository;
using RealEstateVisningApp.LLMProcessing.Services;
using System.Text;
using System.Text.Json;

namespace RealEstateVisningApp.LLMProcessing;

[ApiController]
[Route("api/[controller]")]
public class RealEstateController : ControllerBase
{
    private readonly IFinnScraperService _finnScraperService;
    private readonly ILlmService _llmService;
    private readonly IRealEstateRepository _realEstateRepository;
    private readonly ISupabaseService _supabaseService;
    private readonly ILogger<RealEstateController> _logger;
    private readonly IHttpClientFactory _httpClientFactory;

    public RealEstateController(
        IFinnScraperService finnScraperService,
        ILlmService llmService,
        IRealEstateRepository realEstateRepository,
        ISupabaseService supabaseService,
        ILogger<RealEstateController> logger,
        IHttpClientFactory httpClientFactory)
    {
        _finnScraperService = finnScraperService;
        _llmService = llmService;
        _realEstateRepository = realEstateRepository;
        _supabaseService = supabaseService;
        _logger = logger;
        _httpClientFactory = httpClientFactory;
    }

    #region Processing Endpoints

    /// <summary>
    /// Updates Salgsoppgave URLs for all real estate properties.
    /// </summary>
    /// <returns>Status of the bulk update operation.</returns>
    [HttpPut("processing/update-all-salgsoppgave-urls")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult> UpdateAllSalgsoppgaveUrls()
    {
        try
        {
            var realEstateDataHelper = new RealEstateDataHelper(_realEstateRepository, _finnScraperService);
            await realEstateDataHelper.SetSalgsoppgaveUrlOnAllRealEstates();
        
            return Ok(new { Message = "Successfully updated all Salgsoppgave URLs" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating Salgsoppgave URLs");
            return StatusCode(StatusCodes.Status500InternalServerError, new { Message = "An error occurred while updating Salgsoppgave URLs" });
        }
    }

    /// <summary>
    /// Orchestrates scraping a Finn.no listing and processing the content via LLM.
    /// </summary>
    /// <param name="request">Request containing the FinnKode.</param>
    /// <returns>Processed data from the LLM or error details.</returns>
    [HttpPost("processing/process-listing")]
    [ProducesResponseType(typeof(ProcessListingResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ProcessListingResponse>> ProcessListing([FromBody] ProcessListingRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        _logger.LogInformation("API Request: ProcessListing for FinnKode {FinnKode}", request.FinnKode);
        var response = new ProcessListingResponse { FinnKode = request.FinnKode };

        try
        {
            // Step 1: Get Broker URL from Finn
            string? brokerUrl = await _finnScraperService.FindProspectusUrlAsync(request.FinnKode);

            if (string.IsNullOrWhiteSpace(brokerUrl))
            {
                _logger.LogWarning("ProcessListing: Could not find broker URL for FinnKode {FinnKode}", request.FinnKode);
                response.ScrapeSuccess = false;
                response.ScrapeErrorMessage = "Could not find prospectus URL on Finn.no.";
                // Return OK, as the process executed but failed at a step
                return Ok(response);
            }

            _logger.LogInformation("ProcessListing: Found broker URL {BrokerUrl} for FinnKode {FinnKode}", brokerUrl, request.FinnKode);

            // Step 2: Scrape Content from Broker URL
            var scrapeResult = await _finnScraperService.ScrapeBrokerContentAsync(request.FinnKode, brokerUrl);

            if (scrapeResult.Content == null)
            {
                _logger.LogWarning("ProcessListing: Scraping failed for FinnKode {FinnKode}, URL {BrokerUrl}", request.FinnKode, brokerUrl);
                response.ScrapeSuccess = false;
                response.ScrapeErrorMessage = "Failed to scrape content from the broker site.";
                return Ok(response);
            }

            response.ScrapeSuccess = true;
            _logger.LogInformation("ProcessListing: Scraping successful for FinnKode {FinnKode}", request.FinnKode);

            // Step 3: Process Content with LLM
            var llmResult = await _llmService.ProcessContentAsync(scrapeResult.Content, request.FinnKode);

            if (llmResult == null)
            {
                _logger.LogWarning("ProcessListing: LLM processing failed for FinnKode {FinnKode}", request.FinnKode);
                response.LlmSuccess = false;
                response.LlmErrorMessage = "LLM failed to process the scraped content.";
                return Ok(response); // Still return OK, indicating process ran but failed at LLM step
            }

            response.LlmSuccess = true;
            response.LlmResult = llmResult;
            _logger.LogInformation("ProcessListing: LLM processing successful for FinnKode {FinnKode}", request.FinnKode);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unhandled exception during ProcessListing for FinnKode {FinnKode}", request.FinnKode);
            // We might have partial success, but an unexpected error occurred.
            response.ScrapeSuccess = false; // Mark everything as failed due to exception
            response.LlmSuccess = false;
            response.ScrapeErrorMessage ??= "An internal server error occurred during scraping.";
            response.LlmErrorMessage ??= "An internal server error prevented LLM processing.";
            // Return 500 as this is an unexpected server error during orchestration
            return StatusCode(StatusCodes.Status500InternalServerError, response);
        }
    }

    #endregion

    #region Storage Endpoints

    /// <summary>
    /// Saves the processed listing data to the database.
    /// </summary>
    /// <param name="request">The processed data and FinnKode.</param>
    /// <returns>Status of the save operation.</returns>
    [HttpPost("storage/save-listing")]
    [ProducesResponseType(typeof(SaveListingResponse), StatusCodes.Status200OK)] // Success
    [ProducesResponseType(typeof(SaveListingResponse), StatusCodes.Status400BadRequest)] // Bad input
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(SaveListingResponse), StatusCodes.Status500InternalServerError)] // DB error
    public async Task<ActionResult<SaveListingResponse>> SaveListing([FromBody] SaveListingRequest request)
    {
        if (!ModelState.IsValid || request.ProcessedData == null)
        {
            if (request.ProcessedData == null)
                ModelState.AddModelError(nameof(request.ProcessedData), "ProcessedData cannot be null.");
            return BadRequest(ModelState);
        }

        _logger.LogInformation("API Request: SaveListing for FinnKode {FinnKode}", request.FinnKode);

        try
        {
            bool success = await _supabaseService.SaveListingAsync(request.FinnKode, request.ProcessedData);

            if (success)
            {
                return Ok(new SaveListingResponse { FinnKode = request.FinnKode, Success = true, Message = "Listing saved successfully." });
            }
            else
            {
                _logger.LogWarning("Saving failed for FinnKode {FinnKode} (handled by service).", request.FinnKode);
                // Return 500 as saving failure is a server-side problem
                return StatusCode(StatusCodes.Status500InternalServerError, new SaveListingResponse { FinnKode = request.FinnKode, Success = false, Message = "Failed to save listing to the database." });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unhandled exception in SaveListing for FinnKode {FinnKode}", request.FinnKode);
            return StatusCode(StatusCodes.Status500InternalServerError, new SaveListingResponse { FinnKode = request.FinnKode, Success = false, Message = "An internal server error occurred while saving the listing." });
        }
    }

    #endregion

    #region Scraper Endpoints

    /// <summary>
    /// Processes real estate listings that need to be scraped.
    /// </summary>
    /// <returns>Status of the processing operation.</returns>
    [HttpPost("scraper/process-pending")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public ActionResult ProcessPendingListings()
    {
        try
        {
            var realEstatesToProcess = _realEstateRepository.GetQueryable().OrderByDescending(a => a.Id)
                .Where(a =>
                    (a.SalgsoppgaveProcessingCompleted == false || a.SalgsoppgaveProcessingCompleted == null) &&
                    (a.SalgsoppgaveProcessingTries < 3 || a.SalgsoppgaveProcessingTries == null) &&
                    a.SalgsoppgaveContent == null && a.OrganisationName == "DNB Eiendom AS").Take(1)
                .ToList();

            return Ok(new { Message = $"Found {realEstatesToProcess.Count} listings to process", Listings = realEstatesToProcess });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing pending listings");
            return StatusCode(StatusCodes.Status500InternalServerError, new { Message = "An error occurred while processing pending listings" });
        }
    }

    /// <summary>
    /// Processes real estate listings that need to be scraped and updates them in the database.
    /// </summary>
    /// <returns>Status of the processing operation.</returns>
    [HttpPut("scraper/process-prospectus")]
    [ProducesResponseType(typeof(ProspectusUrlResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProspectusUrlResponse), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ProspectusUrlResponse>> ProcessRealEstates()
    {
        var realEstatesToProcess = _realEstateRepository.GetQueryable().OrderByDescending(a => a.Id)
            .Where(a =>
                (a.SalgsoppgaveProcessingCompleted == false || a.SalgsoppgaveProcessingCompleted == null) &&
                (a.SalgsoppgaveProcessingTries < 3 || a.SalgsoppgaveProcessingTries == null) &&
                a.SalgsoppgaveContent == null && a.OrganisationName == "DNB Eiendom AS").Take(1)
            .ToList();
        
        using (var httpClient = new HttpClient()) // Create HttpClient within a using block for proper disposal
        {
            // Process each real estate sequentially to avoid overloading external resources
            foreach (var realEstate in realEstatesToProcess)
            {
                try
                {
                    realEstate.SalgsoppgaveProcessingTries++;

                    string? url = !string.IsNullOrEmpty(realEstate.SalgsoppgaveUrl) 
                        ? realEstate.SalgsoppgaveUrl 
                        : await _finnScraperService.FindProspectusUrlAsync(realEstate.FinnAdId.ToString());

                    if (url != null)
                    {
                        realEstate.SalgsoppgaveUrl = url;

                        var processListingRequest = new ProcessListingRequest { FinnKode = realEstate.FinnAdId.ToString() };
                      
                        var jsonRequest = JsonSerializer.Serialize(processListingRequest);
                        var content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");
                        
                        // Use the current host for the API call
                        string currentUrl = $"{HttpContext.Request.Scheme}://{HttpContext.Request.Host}/api/RealEstate/processing/process-listing";
                        var response = await httpClient.PostAsync(currentUrl, content);
                        
                        if (response.IsSuccessStatusCode)
                        {
                            var listingResult = await response.Content.ReadFromJsonAsync<ProcessListingResponse>();

                            realEstate.SalgsoppgaveContent = listingResult?.LlmResult;
                            realEstate.SalgsoppgaveProcessedDate = DateTime.UtcNow; 
                            realEstate.SalgsoppgaveProcessingCompleted = true;                  
                        }
                        else
                        {
                            realEstate.SalgsoppgaveProcessingTries = realEstate.SalgsoppgaveProcessingTries == null ? 1 : realEstate.SalgsoppgaveProcessingTries + 1;
                            realEstate.SalgsoppgaveProcessingCompleted = false;
                        }
                    }
              
                    await _realEstateRepository.UpdateAsync(realEstate); 
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing real estate with FinnAdId {FinnAdId}", realEstate.FinnAdId);
                }
            }
        }
        return Ok();
    }

    /// <summary>
    /// Gets the prospectus URL from a Finn.no ad.
    /// </summary>
    /// <param name="request">The request containing the FinnKode.</param>
    /// <returns>The URL of the prospectus or an error.</returns>
    [HttpPost("scraper/get-prospectus-url")]
    [ProducesResponseType(typeof(ProspectusUrlResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProspectusUrlResponse), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ProspectusUrlResponse>> GetProspectusUrlFromFinn([FromBody] GetProspectusUrlRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        _logger.LogInformation("API Request: GetProspectusUrlFromFinn for FinnKode {FinnKode}", request.FinnKode);

        try
        {
            string? url = await _finnScraperService.FindProspectusUrlAsync(request.FinnKode);

            if (url != null)
            {
                return Ok(new ProspectusUrlResponse { FinnKode = request.FinnKode, ProspectusUrl = url, Success = true });
            }
            else
            {
                _logger.LogWarning("Prospectus URL not found for FinnKode {FinnKode}", request.FinnKode);
                return NotFound(new ProspectusUrlResponse { FinnKode = request.FinnKode, Success = false, ErrorMessage = "Prospectus URL not found on Finn.no page." });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unhandled exception in GetProspectusUrlFromFinn for FinnKode {FinnKode}", request.FinnKode);
            return StatusCode(StatusCodes.Status500InternalServerError, new ProspectusUrlResponse { FinnKode = request.FinnKode, Success = false, ErrorMessage = "An internal server error occurred while fetching the prospectus URL." });
        }
    }

    /// <summary>
    /// Scrapes the content from a given prospectus URL (typically obtained from the previous step).
    /// </summary>
    /// <param name="request">The request containing the URL to scrape and the original FinnKode.</param>
    /// <returns>The scraped content or an error.</returns>
    [HttpPost("scraper/scrape-prospectus-content")]
    [ProducesResponseType(typeof(ScrapedDataResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ScrapedDataResponse), StatusCodes.Status400BadRequest)] // Bad URL format etc.
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ScrapedDataResponse>> ScrapeProspectusContent([FromBody] ScrapeProspectusContentRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        _logger.LogInformation("API Request: ScrapeProspectusContent for FinnKode {FinnKode}, URL {ProspectusUrl}", request.FinnKode, request.ProspectusUrl);

        if (!Uri.TryCreate(request.ProspectusUrl, UriKind.Absolute, out _))
        {
            ModelState.AddModelError(nameof(request.ProspectusUrl), "Invalid URL format.");
            return BadRequest(ModelState);
        }

        try
        {
            var result = await _finnScraperService.ScrapeBrokerContentAsync(request.FinnKode, request.ProspectusUrl);

            if (result.Content != null)
            {
                return Ok(new ScrapedDataResponse { FinnKode = result.FinnKode, BrokerUrl = result.BrokerUrl, Content = result.Content, Success = true });
            }
            else
            {
                _logger.LogWarning("Scraping failed for FinnKode {FinnKode}, URL {ProspectusUrl}. Content is null.", request.FinnKode, request.ProspectusUrl);
                return Ok(new ScrapedDataResponse { FinnKode = request.FinnKode, BrokerUrl = request.ProspectusUrl, Success = false, ErrorMessage = "Failed to scrape content from the broker URL." });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unhandled exception in ScrapeProspectusContent for FinnKode {FinnKode}, URL {ProspectusUrl}", request.FinnKode, request.ProspectusUrl);
            return StatusCode(StatusCodes.Status500InternalServerError, new ScrapedDataResponse { FinnKode = request.FinnKode, BrokerUrl = request.ProspectusUrl, Success = false, ErrorMessage = "An internal server error occurred while scraping the content." });
        }
    }

    #endregion
}
