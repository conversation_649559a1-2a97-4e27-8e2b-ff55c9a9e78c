using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using RealEstateVisningApp.Database.Model;

namespace RealEstateVisningApp.Database;

public class RealEstateDbContext : DbContext
{
    public DbSet<RealEstateListing> RealEstateListings { get; set; }
    public DbSet<RealEstateListingPriceHistory> RealEstateListingPriceHistories { get; set; }
    public RealEstateDbContext() : base()
    {
    }
    
    public RealEstateDbContext(DbContextOptions<RealEstateDbContext> options) : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure the converters for JSONB columns
        modelBuilder.Entity<RealEstateListing>()
            .Property(e => e.ViewingTimes)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions)null));

        modelBuilder.Entity<RealEstateListing>()
            .Property(e => e.ImageUrls)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions)null));

        // Configure the column type for JSONB
        modelBuilder.Entity<RealEstateListing>()
            .Property(e => e.ViewingTimes)
            .HasColumnType("jsonb");

        modelBuilder.Entity<RealEstateListing>()
            .Property(e => e.ImageUrls)
            .HasColumnType("jsonb");
        
        modelBuilder.Entity<RealEstateListingPriceHistory>()
            .Property(e => e.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP");
    }
}