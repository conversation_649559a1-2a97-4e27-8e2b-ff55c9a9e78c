// using System.Data.Common;
// using Microsoft.EntityFrameworkCore;
// using Microsoft.EntityFrameworkCore.Diagnostics;
// using Npgsql;
// using Microsoft.EntityFrameworkCore;
// using Microsoft.Extensions.DependencyInjection;
// using Npgsql;
// using Polly;
// using System;
//
// namespace RealEstateVisningApp.Database;
//
// public static class DbContextOptionsBuilderExtensions
// {
//     public static DbContextOptionsBuilder UseNpgsqlWithRetries(
//         this DbContextOptionsBuilder optionsBuilder,
//         string connectionString,
//         int maxRetryCount = 3,
//         int maxRetryDelay = 30)
//     {
//         optionsBuilder.UseNpgsql(connectionString, options =>
//         {
//             options.EnableRetryOnFailure(maxRetryCount);
//             options.CommandTimeout(30);
//         });
//
//         var retryPolicy = Policy
//             .Handle<NpgsqlException>()
//             .Or<TimeoutException>()
//             .WaitAndRetry(
//                 retryCount: maxRetryCount,
//                 sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
//                 onRetry: (exception, timeSpan, retryCount, context) =>
//                 {
//                     // Log retry attempt
//                     Console.WriteLine($"Retry attempt {retryCount} due to {exception.GetType().Name}");
//                 });
//
//         optionsBuilder.AddInterceptors(new RetryingExecutionStrategy(retryPolicy));
//
//         return optionsBuilder;
//     }
// }
//
// public class RetryingExecutionStrategy : DbCommandInterceptor
// {
//     private readonly IAsyncPolicy _retryPolicy;
//
//     public RetryingExecutionStrategy(IAsyncPolicy retryPolicy)
//     {
//         _retryPolicy = retryPolicy;
//     }
//
//     public override ValueTask<InterceptionResult<int>> NonQueryExecutingAsync(
//         DbCommand command, 
//         CommandEventData eventData, 
//         InterceptionResult<int> result, 
//         CancellationToken cancellationToken = default)
//     {
//         return new ValueTask<InterceptionResult<int>>(
//             InterceptionResult<int>.SuppressWithResult(
//                 _retryPolicy.ExecuteAsync(() => command.ExecuteNonQueryAsync(cancellationToken)).Result));
//     }
//
//     // Implement similar methods for ScalarExecutingAsync and ReaderExecutingAsync if needed
// }
