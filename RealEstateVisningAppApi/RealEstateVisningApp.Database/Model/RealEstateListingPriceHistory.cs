using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using RealEstateVisningApp.Database.Model;

[Table("real_estate_listing_price_history", Schema = "public")]
public class RealEstateListingPriceHistory
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    [Column("id")]
    public int Id { get; set; }

    [Required]
    [ForeignKey("RealEstateListing")]
    [Column("real_estate_listing_id")]
    public int RealEstateListingId { get; set; }

    [Column("price_suggestion_amount")]
    public int? PriceSuggestionAmount { get; set; }

    [Column("price_suggestion_currency_code", TypeName = "char(3)")]
    public string? PriceSuggestionCurrencyCode { get; set; }

    [Column("price_suggestion_price_unit", TypeName = "varchar(10)")]
    public string? PriceSuggestionPriceUnit { get; set; }

    [Column("price_total_amount")]
    public int? PriceTotalAmount { get; set; }

    [Column("price_total_currency_code", TypeName = "char(3)")]
    public string? PriceTotalCurrencyCode { get; set; }

    [Column("price_total_price_unit", TypeName = "varchar(10)")]
    public string? PriceTotalPriceUnit { get; set; }

    [Column("price_shared_cost_amount")]
    public int? PriceSharedCostAmount { get; set; }

    [Column("price_shared_cost_currency_code", TypeName = "char(3)")]
    public string? PriceSharedCostCurrencyCode { get; set; }

    [Column("price_shared_cost_price_unit", TypeName = "varchar(10)")]
    public string? PriceSharedCostPriceUnit { get; set; }

    [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
    [Column("created_at")]
    public DateTimeOffset? CreatedAt { get; set; }

    public virtual RealEstateListing RealEstateListing { get; set; }
}