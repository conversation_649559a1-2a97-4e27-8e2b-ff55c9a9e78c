using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RealEstateVisningApp.Database.Model;

[Table("real_estate_listing")]
// [Supabase.Postgrest.Attributes.Table("real_estate_listing")]
    public class RealEstateListing
    {
        public RealEstateListing()
        {
            PriceHistories = new HashSet<RealEstateListingPriceHistory>();
        }
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Column("type")]
        public string? Type { get; set; }

        [Column("main_search_key")]
        public string? MainSearchKey { get; set; }

        [Column("heading")]
        public string? Heading { get; set; }

        [Column("location")]
        public string? Location { get; set; }

        [Column("image_url")]
        public string? ImageUrl { get; set; }

        [Column("image_path")]
        public string? ImagePath { get; set; }

        [Column("image_height")]
        public int? ImageHeight { get; set; }

        [Column("image_width")]
        public int? ImageWidth { get; set; }

        [Column("image_aspect_ratio")]
        public double? ImageAspectRatio { get; set; }

        [Column("timestamp")]
        public long? Timestamp { get; set; }

        [Column("canonical_url")]
        public string? CanonicalUrl { get; set; }

        [Column("logo_url")]
        public string? LogoUrl { get; set; }

        [Column("logo_path")]
        public string? LogoPath { get; set; }

        [Column("price_suggestion_amount")]
        public int? PriceSuggestionAmount { get; set; }

        [Column("price_suggestion_currency_code")]
        public string? PriceSuggestionCurrencyCode { get; set; }

        [Column("price_suggestion_price_unit")]
        public string? PriceSuggestionPriceUnit { get; set; }

        [Column("price_total_amount")]
        public int? PriceTotalAmount { get; set; }

        [Column("price_total_currency_code")]
        public string? PriceTotalCurrencyCode { get; set; }

        [Column("price_total_price_unit")]
        public string? PriceTotalPriceUnit { get; set; }

        [Column("price_shared_cost_amount")]
        public int? PriceSharedCostAmount { get; set; }

        [Column("price_shared_cost_currency_code")]
        public string? PriceSharedCostCurrencyCode { get; set; }

        [Column("price_shared_cost_price_unit")]
        public string? PriceSharedCostPriceUnit { get; set; }

        [Column("area_range_size_from")]
        public int? AreaRangeSizeFrom { get; set; }

        [Column("area_range_size_to")]
        public int? AreaRangeSizeTo { get; set; }

        [Column("area_range_unit")]
        public string? AreaRangeUnit { get; set; }

        [Column("area_range_description")]
        public string? AreaRangeDescription { get; set; }

        [Column("area_plot_size")]
        public int? AreaPlotSize { get; set; }

        [Column("area_plot_unit")]
        public string? AreaPlotUnit { get; set; }

        [Column("area_plot_description")]
        public string? AreaPlotDescription { get; set; }

        [Column("organisation_name")]
        public string? OrganisationName { get; set; }

        [Column("number_of_bedrooms")]
        public int? NumberOfBedrooms { get; set; }

        [Column("owner_type_description")]
        public string? OwnerTypeDescription { get; set; }

        [Column("property_type_description")]
        public string? PropertyTypeDescription { get; set; }

        [Column("viewing_times")]
        public List<string?>? ViewingTimes { get; set; }

        [Column("latitude")]
        public double? Latitude { get; set; }

        [Column("longitude")]
        public double? Longitude { get; set; }

        [Column("ad_type")]
        public int? AdType { get; set; }

        [Column("image_urls")]
        public List<string>? ImageUrls { get; set; }

        [Column("finn_ad_id")]
        public int FinnAdId { get; set; }
        
        [Column("is_sold")]
        public bool? IsSold { get; set; }
        
        [Column("is_sold_timestamp")]
        public long? IsSoldTimestamp { get; set; }
        
        [Column("last_crawled_timestamp")]
        public long? LastCrawledTimestamp { get; set; }
        
        [Column("updated_timestamp")]
        public long? UpdatedTimestamp { get; set; }
        
        //New fields from supabase table:
        
        [Column("energy_marking")]
        public string? EnergyMarking { get; set; }
        
        [Column("realtor_id")]
        public Guid? RealtorId { get; set; }

        [Column("salgsoppgave_url")]
        public string? SalgsoppgaveUrl { get; set; }

        [Column("salgsoppgave_content")]
        public string? SalgsoppgaveContent { get; set; }


        [Column("salgsoppgave_recommendation")]
        public string? SalgsoppgaveRecommendation { get; set; }

        [Column("takstrapport_url")]
        public string? TakstrapportUrl { get; set; }

        [Column("takstrapport_content")]
        public string? TakstrapportContent { get; set; }


        [Column("salgsoppgave_processed_date")]
        public DateTime? SalgsoppgaveProcessedDate { get; set; }


        [Column("salgsoppgave_processing_tries")]
        public int? SalgsoppgaveProcessingTries { get; set; }


        [Column("salgsoppgave_processing_completed")]
        public bool? SalgsoppgaveProcessingCompleted { get; set; }
        
        public virtual ICollection<RealEstateListingPriceHistory> PriceHistories { get; set; }
    }
        