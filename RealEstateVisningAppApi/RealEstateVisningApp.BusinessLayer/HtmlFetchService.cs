using <PERSON>;
using Polly.Retry;

namespace RealEstateVisningApp.BusinessLayer;

public interface IHtmlFetchService
{
    Task<string> FetchHtmlAsync(string url);
}

public class HtmlFetchService : IHtmlFetchService
{
    private readonly HttpClient _httpClient;
    private readonly AsyncRetryPolicy _retryPolicy;

    public HtmlFetchService()
    {
        _httpClient = new HttpClient();
        _retryPolicy = Policy
            .Handle<HttpRequestException>()
            .WaitAndRetryAsync(3, retryAttempt => 
                TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)));
    }

    public async Task<string> FetchHtmlAsync(string url)
    {
        return await _retryPolicy.ExecuteAsync(async () => 
            await _httpClient.GetStringAsync(url));
    }
}