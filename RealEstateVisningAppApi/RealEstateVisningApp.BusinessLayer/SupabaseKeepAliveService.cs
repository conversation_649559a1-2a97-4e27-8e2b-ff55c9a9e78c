using RealEstateVisningApp.BusinessLayer.SupabaseModel;
using RealEstateVisningApp.Database.Model;

namespace RealEstateVisningApp.BusinessLayer;

public class SupabaseKeepAliveService
{
    private readonly SupabaseService _supabaseService;

    public SupabaseKeepAliveService(SupabaseService supabaseService)
    {
        _supabaseService = supabaseService;
    }

    public async Task KeepAliveAsync()
    {
        var client = _supabaseService.HangfireClient;
        try
        {
            var result = await client
                .From<RealEstateListingSupabase>()
                .Limit(1)
                .Get();
            
         
           Console.WriteLine($"KeepAlive: Retrieved {result.Models.Count} record(s)");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"KeepAlive failed: {ex.Message}");
            throw;
        }
    }
}