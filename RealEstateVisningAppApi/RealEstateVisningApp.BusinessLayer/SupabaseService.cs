using Microsoft.Extensions.Configuration;
using RealEstateVisningApp.Database.Model;
using Supabase;

namespace RealEstateVisningApp.BusinessLayer;

public class SupabaseService
{
    private readonly Supabase.Client _client;

    public SupabaseService(IConfiguration configuration)
    {
        var url = configuration["Supabase:Url"] ?? throw new Exception("Supabase:Url not set");
        var key = configuration["Supabase:AnonKey"] ?? throw new Exception("Supabase:AnonKey not set");

        var options = new SupabaseOptions { AutoConnectRealtime = false };
        _client = new Supabase.Client(url, key, options);
        _client.InitializeAsync().Wait();
    }

    public Supabase.Client HangfireClient => _client;


    
}