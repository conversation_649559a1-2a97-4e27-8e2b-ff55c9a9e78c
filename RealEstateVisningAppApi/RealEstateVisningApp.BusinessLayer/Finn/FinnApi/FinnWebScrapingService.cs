using HtmlAgilityPack;
using RealEstateVisningApp.BusinessLayer.Finn.CrawlFinn.FinnWebScrapper;

namespace RealEstateVisningApp.BusinessLayer.Finn.CrawlFinn;

/// <summary>
///   This class is responsible for scraping the Finn website.
/// </summary>
public class FinnWebScrapingService
{
    private readonly IHtmlFetchService _htmlFetchService;
    private readonly IMatrikkelInfoExtractorService _matrikkelInfoExtractorService;
    public FinnWebScrapingService(
        IHtmlFetchService htmlFetchService,
        IMatrikkelInfoExtractorService matrikkelInfoExtractorService)
    {
        _htmlFetchService = htmlFetchService;
        _matrikkelInfoExtractorService = matrikkelInfoExtractorService;
    }
    
    public async Task<FinnAdData> ScrapFinnAdData(string url)
    {
        var html = await _htmlFetchService.FetchHtmlAsync(url);
        var htmlDocument = new HtmlDocument();
        htmlDocument.LoadHtml(html);
        var matrikkelInfo = _matrikkelInfoExtractorService.ExtractMatrikkelInfo(htmlDocument);

        return new FinnAdData
        {
            MatrikkelInfo = matrikkelInfo
        };
    }
    
}

public class FinnAdData
{
    public MatrikkelInfo MatrikkelInfo { get; set; }
}