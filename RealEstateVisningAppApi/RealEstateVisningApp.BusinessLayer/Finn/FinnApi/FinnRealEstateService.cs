using Hangfire;
using Hangfire.Console;
using Hangfire.Server;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using RealEstateVisningApp.BusinessLayer.Finn.CrawlFinn.Mapper;
using RealEstateVisningApp.BusinessLayer.Finn.FinnApi.Crawlers;
using RealEstateVisningApp.Database.Model;
using RealEstateVisningApp.Database.Repository;

namespace RealEstateVisningApp.BusinessLayer.Finn.CrawlFinn;

/// <summary>
/// This service is using the finn api to get the real estate information.
/// </summary>
public class FinnRealEstateService
{
    private readonly FinnRealEstateCrawlerService _finnRealEstateCrawlerService;
    private readonly IRealEstateRepository _realEstateRepository;
    private readonly ILogger<FinnRealEstateService> _logger;
    private readonly IRealEstateMapper _realEstateMapper;

    public FinnRealEstateService(
        FinnRealEstateCrawlerService finnRealEstateCrawlerService, 
        IRealEstateRepository realEstateRepository,
        ILogger<FinnRealEstateService> logger,
        IRealEstateMapper realEstateMapper)
    {
        _finnRealEstateCrawlerService = finnRealEstateCrawlerService;
        _realEstateRepository = realEstateRepository;
        _logger = logger;
        _realEstateMapper = realEstateMapper;
       ;
    }
    
    [DisableConcurrentExecution(timeoutInSeconds: 60*8)]
    [AutomaticRetry(Attempts = 0)]
    public async Task UpdateNewestRealEstateFromFinn(PerformContext context)
    {
        var finnModel = await _finnRealEstateCrawlerService.FetchNewest();
        
        if (finnModel?.Docs == null || !finnModel.Docs.Any())
        {
            context.WriteLine("No documents fetched from Finn API.");
            return;
        }
        
        var apiIds = finnModel.Docs.Select(d => d.AdId).ToHashSet();
        
        // Fetch existing IDs from the database using the repository method
        var existingIds = await _realEstateRepository.GetExistingIds(apiIds);
        
        var newIds = apiIds.Except(existingIds).ToHashSet();
        var newListings = new List<RealEstateListing>();
        
        foreach (var doc in finnModel.Docs.Where(d => newIds.Contains(d.AdId)))
        {
            try
            {
                var realEstateListing = _realEstateMapper.MapToRealEstateListing(doc);
                newListings.Add(realEstateListing);
            }
            catch (Exception e)
            {
                _logger.LogError(e, $"Error mapping document with AdId {doc.AdId}: {e.Message}");
                context.WriteLine($"Error mapping document with AdId {doc.AdId}: {e.Message}");
            }
        }

        if (newListings.Any())
        {
            await _realEstateRepository.AddRangeAsync(newListings);
            _logger.LogInformation( $"Added {newListings.Count} new listings to the database.");
            context.WriteLine($"Added {newListings.Count} new listings to the database.");
            foreach (var listings in newListings)
            {
                context.WriteLine( $"Added {listings.FinnAdId} -  {listings.Heading} to the database.");
            }
        }
        else
        {
            _logger.LogInformation("No new listings to add to the database.");
            context.WriteLine("No new listings to add to the database.");
        }
    }

    [DisableConcurrentExecution(timeoutInSeconds: 60*60*2)]
    [AutomaticRetry(Attempts = 0)]
    public async Task AddOrUpdateAllRealEstateFromFinn(PerformContext context, List<LocationHierarchy.Location> Locations)
    {
        foreach (var location in Locations)
        {
            await ProcessLocation(location, context);
            
            foreach (var subLocation in location.Sublocations)
            {
                await ProcessLocation(subLocation, context);
            }
        }
    }
    private async Task ProcessLocation(LocationHierarchy.Location location, PerformContext context)
    {
        var page = 1;
        const int maxPages = 150;
        _logger.LogInformation($"Processing location {location.Name} with {location.Count} listings.");
        context.WriteLine($"Processing location {location.Name} with {location.Count} listings.");

        var errorCountsWithoutSuccess = 0;
        while (page <= maxPages || errorCountsWithoutSuccess  == 5)
        {
            try
            {
                var result = await _finnRealEstateCrawlerService.FetchSpecific(location.Id, page);

                if (result.Docs == null || !result.Docs.Any())
                {
                    _logger.LogDebug($"No documents fetched from Finn API for location {location.Name} on page {page}.");
                    context.WriteLine($"No documents fetched from Finn API for location {location.Name} on page {page}.");
                    break; // Exit the loop if no more results
                }
                
                _logger.LogDebug($"Processing {result.Docs.Count} listings for {location.Name} on page {page}");
                context.WriteLine($"Processing {result.Docs.Count} listings for {location.Name} on page {page}");

                var finnAdIds = result.Docs?.Select(d => d.AdId);
                
                var existingRealEstates = await _realEstateRepository.GetQueryable()
                    .Where(a => finnAdIds.Contains(a.FinnAdId))
                    .Select(a => new
                    {
                        a.Id,
                        a.FinnAdId,
                        a.PriceTotalAmount,
                        a.PriceTotalCurrencyCode,
                        a.PriceTotalPriceUnit,
                        a.PriceSuggestionAmount,
                        a.PriceSuggestionCurrencyCode,
                        a.PriceSuggestionPriceUnit,
                        a.PriceSharedCostAmount,
                        a.PriceSharedCostCurrencyCode,
                        a.PriceSharedCostPriceUnit,
                        a.IsSold
                    })
                    .AsNoTracking()
                    .ToListAsync();

                var existingRealEstatesDict = existingRealEstates.ToDictionary(a => a.FinnAdId);
                
                foreach (var realEstateFromFinn in result.Docs)
                {
                    try
                    {
                        var existingRealEstate = existingRealEstatesDict.GetValueOrDefault(realEstateFromFinn.AdId);
                        
                        if (existingRealEstate != null)
                        {
                            bool totalPriceChanged = existingRealEstate.PriceTotalAmount != realEstateFromFinn.PriceTotal?.Amount && realEstateFromFinn.PriceTotal != null &&
                                                realEstateFromFinn.PriceTotal?.Amount != 0;
                            
                            bool priceSuggestionChanged = existingRealEstate.PriceSuggestionAmount != realEstateFromFinn.PriceSuggestion?.Amount &&
                                                          realEstateFromFinn.PriceSuggestion != null && realEstateFromFinn.PriceSuggestion?.Amount != 0;
                            
                            bool soldStatusChanged = existingRealEstate.IsSold != realEstateFromFinn.IsSold;
                          
                            
                            
                            if (totalPriceChanged || soldStatusChanged || priceSuggestionChanged)
                            {
                                var fullEntity = await _realEstateRepository.GetByIdAsync(existingRealEstate.Id);

                                if (totalPriceChanged || priceSuggestionChanged)
                                {
                                    var priceHistory = new RealEstateListingPriceHistory
                                    {
                                        RealEstateListingId = fullEntity.Id,
                                        PriceSuggestionAmount = fullEntity.PriceSuggestionAmount,
                                        PriceSuggestionCurrencyCode = fullEntity.PriceSuggestionCurrencyCode,
                                        PriceSuggestionPriceUnit = fullEntity.PriceSuggestionPriceUnit,
                                        PriceTotalAmount = fullEntity.PriceTotalAmount,
                                        PriceTotalCurrencyCode = fullEntity.PriceTotalCurrencyCode,
                                        PriceTotalPriceUnit = fullEntity.PriceTotalPriceUnit,
                                        PriceSharedCostAmount = fullEntity.PriceSharedCostAmount,
                                        PriceSharedCostCurrencyCode = fullEntity.PriceSharedCostCurrencyCode,
                                        PriceSharedCostPriceUnit = fullEntity.PriceSharedCostPriceUnit
                                    };
                                    fullEntity.PriceHistories.Add(priceHistory);
                                    
                                    fullEntity.PriceTotalAmount = realEstateFromFinn.PriceTotal?.Amount;
                                    fullEntity.PriceTotalCurrencyCode = realEstateFromFinn.PriceTotal?.CurrencyCode;
                                    fullEntity.PriceTotalPriceUnit = realEstateFromFinn.PriceTotal?.PriceUnit;
                                    fullEntity.PriceSuggestionAmount = realEstateFromFinn.PriceSuggestion?.Amount;
                                    fullEntity.PriceSuggestionCurrencyCode = realEstateFromFinn.PriceSuggestion?.CurrencyCode;
                                    fullEntity.PriceSuggestionPriceUnit = realEstateFromFinn.PriceSuggestion?.PriceUnit;
                                    fullEntity.PriceSharedCostAmount = realEstateFromFinn.PriceSharedCost?.Amount;
                                    fullEntity.PriceSharedCostCurrencyCode = realEstateFromFinn.PriceSharedCost?.CurrencyCode;
                                    fullEntity.PriceSharedCostPriceUnit = realEstateFromFinn.PriceSharedCost?.PriceUnit;
                                    fullEntity.UpdatedTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                                    

                                    _logger.LogDebug($"Updated price for listing {fullEntity.FinnAdId}");
                                    context.WriteLine($"Updated price for listing {fullEntity.FinnAdId}");
                                }

                                if (soldStatusChanged)
                                {
                                    fullEntity.IsSold = realEstateFromFinn.IsSold;
                                    _logger.LogDebug($"Updated sold status for listing {fullEntity.FinnAdId}");
                                    context.WriteLine($"Updated sold status for listing {fullEntity.FinnAdId}");
                                }

                                await _realEstateRepository.AddOrUpdateAsync(fullEntity);
                            }
                        }
                        else
                        {
                            var mappedData = _realEstateMapper.MapToRealEstateListing(realEstateFromFinn);
                            await _realEstateRepository.AddOrUpdateAsync(mappedData);
                            
                            _logger.LogDebug($"Added new listing {mappedData.FinnAdId}");
                            context.WriteLine($"Added new listing {mappedData.FinnAdId}");
                        }
                        
                        errorCountsWithoutSuccess = 0;
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(e, $"Error mapping document with AdId {realEstateFromFinn.AdId}: {e.Message}");
                        context.WriteLine($"Error mapping document with AdId {realEstateFromFinn.AdId}: {e.Message}");
                        errorCountsWithoutSuccess++;
                        
                        if(errorCountsWithoutSuccess == 5)
                        {
                            break;
                        }
                    }
                }

                if (result.Metadata.IsEndOfPaging)
                {
                    _logger.LogDebug($"Reached end of paging for location {location.Name}");
                    context.WriteLine($"Reached end of paging for location {location.Name}");
                    break; // Exit the loop if we've reached the end of paging
                }
                errorCountsWithoutSuccess = 0;
                page++;
            }
            catch (Exception e)
            {
                _logger.LogError(e, $"Error fetching data from Finn API for location {location.Name} on page {page}: {e.Message}");
                context.WriteLine($"Error fetching data from Finn API for location {location.Name} on page {page}: {e.Message}");
                // Optionally, you might want to add a retry mechanism here
                // break; // Exit the loop on error
                page++;
                errorCountsWithoutSuccess++;
            }
        }
        
        _logger.LogInformation($"Completed processing location {location.Name}");
        context.WriteLine($"Completed processing location {location.Name}");
    }
}


