using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace RealEstateVisningApp.BusinessLayer.Finn.CrawlFinn.Model;
// Root myDeserializedClass = JsonSerializer.Deserialize<Root>(myJsonResponse);
    public class Area
    {
        [JsonPropertyName("size")]
        public int Size { get; set; }

        [JsonPropertyName("unit")]
        public string Unit { get; set; }

        [JsonPropertyName("description")]
        public string Description { get; set; }
    }

    public class AreaPlot
    {
        [JsonPropertyName("size")]
        public int? Size { get; set; }

        [JsonPropertyName("unit")]
        public string? Unit { get; set; }

        [JsonPropertyName("description")]
        public string? Description { get; set; }
    }

    public class AreaRange
    {
        [JsonPropertyName("size_from")]
        public int? SizeFrom { get; set; } // value can be null

        [JsonPropertyName("size_to")]
        public int? SizeTo { get; set; }

        [JsonPropertyName("unit")]
        public string? Unit { get; set; }

        [JsonPropertyName("description")]
        public string? Description { get; set; }
    }

    public class BedroomsRange
    {
        [JsonPropertyName("start")]
        public int Start { get; set; }

        [JsonPropertyName("end")]
        public int End { get; set; }
    }

    public class Breadcrumb
    {
        [JsonPropertyName("@type")]
        public string Type { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("itemListElement")]
        public List<ItemListElement> ItemListElement { get; set; }
    }

    public class Coordinates
    {
        [JsonPropertyName("lat")]
        public double Lat { get; set; }

        [JsonPropertyName("lon")]
        public double Lon { get; set; }
    }

    public class Descriptions
    {
        [JsonPropertyName("title")]
        public string Title { get; set; }

        [JsonPropertyName("heading")]
        public string Heading { get; set; }

        [JsonPropertyName("saved_search")]
        public string SavedSearch { get; set; }

        [JsonPropertyName("search_key")]
        public string SearchKey { get; set; }

        [JsonPropertyName("vertical")]
        public string Vertical { get; set; }

        [JsonPropertyName("canonical_search_params")]
        public string CanonicalSearchParams { get; set; }
    }

    public class Doc
    {
        [JsonPropertyName("type")]
        public string Type { get; set; }

        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("main_search_key")]
        public string MainSearchKey { get; set; }

        [JsonPropertyName("heading")]
        public string Heading { get; set; }

        [JsonPropertyName("location")]
        public string Location { get; set; }

        [JsonPropertyName("image")]
        public Image Image { get; set; }

        [JsonPropertyName("flags")]
        public List<string> Flags { get; set; }
        
        public bool IsSold => Flags.Contains("sold");

        [JsonPropertyName("styling")]
        public List<string> Styling { get; set; }

        [JsonPropertyName("timestamp")]
        public object Timestamp { get; set; }

        [JsonPropertyName("labels")]
        public List<Label> Labels { get; set; }

        [JsonPropertyName("canonical_url")]
        public string CanonicalUrl { get; set; }

        [JsonPropertyName("extras")]
        public List<object> Extras { get; set; }

        [JsonPropertyName("logo")]
        public Logo Logo { get; set; }

        [JsonPropertyName("price_suggestion")]
        public PriceSuggestion PriceSuggestion { get; set; }

        [JsonPropertyName("price_total")]
        public PriceTotal PriceTotal { get; set; }

        [JsonPropertyName("price_shared_cost")]
        public PriceSharedCost PriceSharedCost { get; set; }

        [JsonPropertyName("area_range")]
        public AreaRange AreaRange { get; set; }

        [JsonPropertyName("area_plot")]
        public AreaPlot AreaPlot { get; set; }

        [JsonPropertyName("organisation_name")]
        public string OrganisationName { get; set; }

        [JsonPropertyName("local_area_name")]
        public string LocalAreaName { get; set; }

        [JsonPropertyName("number_of_bedrooms")]
        public int NumberOfBedrooms { get; set; }

        [JsonPropertyName("owner_type_description")]
        public string OwnerTypeDescription { get; set; }

        [JsonPropertyName("property_type_description")]
        public string PropertyTypeDescription { get; set; }

        [JsonPropertyName("viewing_times")]
        public List<DateTime> ViewingTimes { get; set; }

        [JsonPropertyName("coordinates")]
        public Coordinates Coordinates { get; set; }

        [JsonPropertyName("ad_type")]
        public int AdType { get; set; }

        [JsonPropertyName("image_urls")]
        public List<string> ImageUrls { get; set; }

        [JsonPropertyName("ad_id")]
        public int AdId { get; set; }

        [JsonPropertyName("area")]
        public Area Area { get; set; }

        [JsonPropertyName("price_range_suggestion")]
        public PriceRangeSuggestion PriceRangeSuggestion { get; set; }

        [JsonPropertyName("price_range_total")]
        public PriceRangeTotal PriceRangeTotal { get; set; }

        [JsonPropertyName("bedrooms_range")]
        public BedroomsRange BedroomsRange { get; set; }
    }

    public class Filter
    {
        [JsonPropertyName("display_name")]
        public string DisplayName { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("filter_items")]
        public List<FilterItem> FilterItems { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }

        [JsonPropertyName("min_value")]
        public int? MinValue { get; set; }

        [JsonPropertyName("max_value")]
        public int? MaxValue { get; set; }

        [JsonPropertyName("step")]
        public int? Step { get; set; }

        [JsonPropertyName("unit")]
        public string Unit { get; set; }

        [JsonPropertyName("name_from")]
        public string NameFrom { get; set; }

        [JsonPropertyName("name_to")]
        public string NameTo { get; set; }

        [JsonPropertyName("is_year")]
        public bool? IsYear { get; set; }

        [JsonPropertyName("item_display_count")]
        public int? ItemDisplayCount { get; set; }
    }

    public class FilterItem
    {
        [JsonPropertyName("display_name")]
        public string DisplayName { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("value")]
        public string Value { get; set; }

        [JsonPropertyName("hits")]
        public int Hits { get; set; }

        [JsonPropertyName("filter_items")]
        public List<FilterItem> FilterItems { get; set; }

        [JsonPropertyName("selected")]
        public bool Selected { get; set; }
    }

    public class GuidedSearch
    {
        [JsonPropertyName("suggestions")]
        public List<object> Suggestions { get; set; }

        [JsonPropertyName("tracking")]
        public Tracking Tracking { get; set; }
    }

    public class Image
    {
        [JsonPropertyName("url")]
        public string Url { get; set; }

        [JsonPropertyName("path")]
        public string Path { get; set; }

        [JsonPropertyName("height")]
        public int Height { get; set; }

        [JsonPropertyName("width")]
        public int Width { get; set; }

        [JsonPropertyName("aspect_ratio")]
        public double AspectRatio { get; set; }
    }

    public class Item
    {
        [JsonPropertyName("@id")]
        public string Id { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }
    }

    public class ItemListElement
    {
        [JsonPropertyName("@type")]
        public string Type { get; set; }

        [JsonPropertyName("position")]
        public int Position { get; set; }

        [JsonPropertyName("item")]
        public Item Item { get; set; }
    }

    public class JsonLd
    {
        [JsonPropertyName("@context")]
        public string Context { get; set; }

        [JsonPropertyName("@type")]
        public List<string> Type { get; set; }

        [JsonPropertyName("url")]
        public string Url { get; set; }

        [JsonPropertyName("mainContentOfPage")]
        public MainContentOfPage MainContentOfPage { get; set; }

        [JsonPropertyName("breadcrumb")]
        public Breadcrumb Breadcrumb { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }
    }

    public class Label
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("text")]
        public string Text { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }
    }

    public class Logo
    {
        [JsonPropertyName("url")]
        public string Url { get; set; }

        [JsonPropertyName("path")]
        public string Path { get; set; }
    }

    public class MainContentOfPage
    {
        [JsonPropertyName("@type")]
        public string Type { get; set; }

        [JsonPropertyName("cssSelector")]
        public string CssSelector { get; set; }
    }

    public class Metadata
    {
        [JsonPropertyName("params")]
        public Params Params { get; set; }

        [JsonPropertyName("search_key")]
        public string SearchKey { get; set; }

        [JsonPropertyName("selected_filters")]
        public List<object> SelectedFilters { get; set; }

        [JsonPropertyName("num_results")]
        public int NumResults { get; set; }

        [JsonPropertyName("quest_time")]
        public int QuestTime { get; set; }

        [JsonPropertyName("solr_time")]
        public int SolrTime { get; set; }

        [JsonPropertyName("solr_elapsed_time")]
        public int SolrElapsedTime { get; set; }

        [JsonPropertyName("result_size")]
        public ResultSize ResultSize { get; set; }

        [JsonPropertyName("paging")]
        public Paging Paging { get; set; }

        [JsonPropertyName("title")]
        public string Title { get; set; }

        [JsonPropertyName("is_savable_search")]
        public bool IsSavableSearch { get; set; }

        [JsonPropertyName("search_key_description")]
        public string SearchKeyDescription { get; set; }

        [JsonPropertyName("vertical")]
        public string Vertical { get; set; }

        [JsonPropertyName("vertical_description")]
        public string VerticalDescription { get; set; }

        [JsonPropertyName("sort")]
        public string Sort { get; set; }

        [JsonPropertyName("descriptions")]
        public Descriptions Descriptions { get; set; }

        [JsonPropertyName("uuid")]
        public string Uuid { get; set; }

        [JsonPropertyName("tracking")]
        public Tracking Tracking { get; set; }

        [JsonPropertyName("guided_search")]
        public GuidedSearch GuidedSearch { get; set; }

        [JsonPropertyName("actions")]
        public List<object> Actions { get; set; }

        [JsonPropertyName("is_end_of_paging")]
        public bool IsEndOfPaging { get; set; }

        [JsonPropertyName("timestamp")]
        public long Timestamp { get; set; }
    }

    public class Object
    {
        [JsonPropertyName("selectionFilters")]
        public List<object> SelectionFilters { get; set; }

        [JsonPropertyName("sortingType")]
        public string SortingType { get; set; }

        [JsonPropertyName("numItems")]
        public int NumItems { get; set; }

        [JsonPropertyName("pageNumber")]
        public int PageNumber { get; set; }

        [JsonPropertyName("layout")]
        public string Layout { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }
    }

    public class PageMetadata
    {
        [JsonPropertyName("title")]
        public string Title { get; set; }

        [JsonPropertyName("description")]
        public string Description { get; set; }

        [JsonPropertyName("indexDirective")]
        public string IndexDirective { get; set; }

        [JsonPropertyName("canonicalUrl")]
        public string CanonicalUrl { get; set; }

        [JsonPropertyName("openGraphUrl")]
        public string OpenGraphUrl { get; set; }

        [JsonPropertyName("jsonLd")]
        public JsonLd JsonLd { get; set; }

        [JsonPropertyName("image")]
        public string Image { get; set; }
    }

    public class Paging
    {
        [JsonPropertyName("param")]
        public string Param { get; set; }

        [JsonPropertyName("current")]
        public int Current { get; set; }

        [JsonPropertyName("last")]
        public int Last { get; set; }
    }

    public class Params
    {
    }

    public class PriceRangeSuggestion
    {
        [JsonPropertyName("amount_from")]
        public int AmountFrom { get; set; }

        [JsonPropertyName("amount_to")]
        public int AmountTo { get; set; }

        [JsonPropertyName("currency_code")]
        public string CurrencyCode { get; set; }
    }

    public class PriceRangeTotal
    {
        [JsonPropertyName("amount_from")]
        public int AmountFrom { get; set; }

        [JsonPropertyName("amount_to")]
        public int AmountTo { get; set; }

        [JsonPropertyName("currency_code")]
        public string CurrencyCode { get; set; }
    }

    public class PriceSharedCost
    {
        [JsonPropertyName("amount")]
        public int Amount { get; set; }

        [JsonPropertyName("currency_code")]
        public string CurrencyCode { get; set; }

        [JsonPropertyName("price_unit")]
        public string PriceUnit { get; set; }
    }

    public class PriceSuggestion
    {
        [JsonPropertyName("amount")]
        public int Amount { get; set; }

        [JsonPropertyName("currency_code")]
        public string CurrencyCode { get; set; }

        [JsonPropertyName("price_unit")]
        public string PriceUnit { get; set; }
    }

    public class PriceTotal
    {
        [JsonPropertyName("amount")]
        public int Amount { get; set; }

        [JsonPropertyName("currency_code")]
        public string CurrencyCode { get; set; }

        [JsonPropertyName("price_unit")]
        public string PriceUnit { get; set; }
    }

    public class ResultSize
    {
        [JsonPropertyName("match_count")]
        public int MatchCount { get; set; }

        [JsonPropertyName("group_count")]
        public int GroupCount { get; set; }
    }

    public class FinnModel
    {
        [JsonPropertyName("docs")]
        public List<Doc> Docs { get; set; }

        [JsonPropertyName("filters")]
        public List<Filter> Filters { get; set; }

        [JsonPropertyName("metadata")]
        public Metadata Metadata { get; set; }

        [JsonPropertyName("mapUrl")]
        public string MapUrl { get; set; }

        [JsonPropertyName("newMapUrl")]
        public string NewMapUrl { get; set; }

        [JsonPropertyName("pageMetadata")]
        public PageMetadata PageMetadata { get; set; }

        [JsonPropertyName("resultHeading")]
        public string ResultHeading { get; set; }
    }

    public class Search
    {
        [JsonPropertyName("items")]
        public List<object> Items { get; set; }

        [JsonPropertyName("@type")]
        public string Type { get; set; }

        [JsonPropertyName("@id")]
        public string Id { get; set; }
    }

    public class Tracking
    {
        [JsonPropertyName("object")]
        public Object Object { get; set; }

        [JsonPropertyName("vertical")]
        public Vertical Vertical { get; set; }

        [JsonPropertyName("search")]
        public Search Search { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("intent")]
        public string Intent { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }
    }

    public class Vertical
    {
        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("subVertical")]
        public string SubVertical { get; set; }
    }

