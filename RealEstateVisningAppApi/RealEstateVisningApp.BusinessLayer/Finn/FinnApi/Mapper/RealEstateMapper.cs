using System.Text.Json;
using RealEstateVisningApp.BusinessLayer.Finn.CrawlFinn.Model;
using RealEstateVisningApp.Database.Model;

namespace RealEstateVisningApp.BusinessLayer.Finn.CrawlFinn.Mapper;

  public interface IRealEstateMapper
    {
        RealEstateListing MapToRealEstateListing(Doc finnDoc);
    }

    public class RealEstateMapper : IRealEstateMapper
    {
        public RealEstateListing MapToRealEstateListing(Doc finnDoc)
        {
            return new RealEstateListing
            {
                FinnAdId = finnDoc.AdId,
                Type = finnDoc.Type,
                MainSearchKey = finnDoc.MainSearchKey,
                Heading = finnDoc.Heading,
                Location = finnDoc.Location,
                ImageUrl = finnDoc.Image?.Url,
                ImagePath = finnDoc.Image?.Path,
                ImageHeight = finnDoc.Image?.Height ?? 0,
                ImageWidth = finnDoc.Image?.Width ?? 0,
                ImageAspectRatio = finnDoc.Image?.AspectRatio ?? 0,
                Timestamp = ConvertToLong(finnDoc.Timestamp),
                CanonicalUrl = finnDoc.CanonicalUrl,
                LogoUrl = finnDoc.Logo?.Url,
                LogoPath = finnDoc.Logo?.Path,
                PriceSuggestionAmount = finnDoc.PriceSuggestion?.Amount ?? 0,
                PriceSuggestionCurrencyCode = finnDoc.PriceSuggestion?.CurrencyCode,
                PriceSuggestionPriceUnit = finnDoc.PriceSuggestion?.PriceUnit,
                PriceTotalAmount = finnDoc.PriceTotal?.Amount ?? 0,
                PriceTotalCurrencyCode = finnDoc.PriceTotal?.CurrencyCode,
                PriceTotalPriceUnit = finnDoc.PriceTotal?.PriceUnit,
                PriceSharedCostAmount = finnDoc.PriceSharedCost?.Amount ?? 0,
                PriceSharedCostCurrencyCode = finnDoc.PriceSharedCost?.CurrencyCode,
                PriceSharedCostPriceUnit = finnDoc.PriceSharedCost?.PriceUnit,
                AreaRangeSizeFrom = finnDoc.AreaRange?.SizeFrom ?? 0,
                AreaRangeSizeTo = finnDoc.AreaRange?.SizeTo ?? 0,
                AreaRangeUnit = finnDoc.AreaRange?.Unit,
                AreaRangeDescription = finnDoc.AreaRange?.Description,
                AreaPlotSize = finnDoc.AreaPlot?.Size ?? 0,
                AreaPlotUnit = finnDoc.AreaPlot?.Unit,
                AreaPlotDescription = finnDoc.AreaPlot?.Description,
                OrganisationName = finnDoc.OrganisationName,
                NumberOfBedrooms = finnDoc.NumberOfBedrooms,
                OwnerTypeDescription = finnDoc.OwnerTypeDescription,
                PropertyTypeDescription = finnDoc.PropertyTypeDescription,
                ViewingTimes = finnDoc.ViewingTimes?.Select(dt => dt.ToString("O")).ToList(),
                Latitude = finnDoc.Coordinates?.Lat ?? 0,
                Longitude = finnDoc.Coordinates?.Lon ?? 0,
                AdType = finnDoc.AdType,
                ImageUrls = finnDoc.ImageUrls,
                IsSold = finnDoc.IsSold,
            };
        }
        
        private long ConvertToLong(object value)
        {
            if (value is JsonElement jsonElement)
            {
                if (jsonElement.ValueKind == JsonValueKind.Number)
                {
                    return jsonElement.GetInt64();
                }
                else if (jsonElement.ValueKind == JsonValueKind.String)
                {
                    if (long.TryParse(jsonElement.GetString(), out long result))
                    {
                        return result;
                    }
                }
            }
            else if (value is long longValue)
            {
                return longValue;
            }
            else if (value is int intValue)
            {
                return intValue;
            }
            else if (value is string stringValue)
            {
                if (long.TryParse(stringValue, out long result))
                {
                    return result;
                }
            }

            // If all else fails, return 0 or throw an exception
            // depending on your error handling strategy
            return 0;
        }
    }