using HtmlAgilityPack;

namespace RealEstateVisningApp.BusinessLayer.Finn.CrawlFinn.FinnWebScrapper;

public class MatrikkelInfo
{
    public string Leilighetsnr { get; set; }
    public string Kommunenr { get; set; }
    public string Gardsnr { get; set; }
    public string Bruksnr { get; set; }
    public string Seksjonsnr { get; set; }
    public string BorettslagNavn { get; set; }
    public string BorettslagOrgnummer { get; set; }
    public string BorettslagAndelsnummer { get; set; }
}
public interface IMatrikkelInfoExtractorService
{
    MatrikkelInfo ExtractMatrikkelInfo(HtmlDocument htmlDocument);
}

public class MatrikkelInfoExtractorService : IMatrikkelInfoExtractorService
{
    public MatrikkelInfo ExtractMatrikkelInfo(HtmlDocument htmlDocument)
    {
        var matrikkelInfoSection = htmlDocument.DocumentNode.SelectSingleNode("//section[@data-testid='cadastre-info']");
        if (matrikkelInfoSection == null)
        {
            throw new Exception("Matrikkel information section not found");
        }

        var matrikkelInfo = new MatrikkelInfo();
        var infoNodes = matrikkelInfoSection.SelectNodes(".//div/div");

        foreach (var node in infoNodes)
        {
            var text = node.InnerText.Trim();
            var parts = text.Split(new[] { ": " }, StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 2)
            {
                var key = parts[0].Trim();
                var value = parts[1].Trim();

                switch (key)
                {
                    case "Leilighetsnr":
                        matrikkelInfo.Leilighetsnr = value;
                        break;
                    case "Kommunenr":
                        matrikkelInfo.Kommunenr = value;
                        break;
                    case "Gårdsnr":
                        matrikkelInfo.Gardsnr = value;
                        break;
                    case "Bruksnr":
                        matrikkelInfo.Bruksnr = value;
                        break;
                    case "Seksjonsnr":
                        matrikkelInfo.Seksjonsnr = value;
                        break;
                    case "Borettslag-navn":
                        matrikkelInfo.BorettslagNavn = value;
                        break;
                    case "Borettslag-orgnummer":
                        matrikkelInfo.BorettslagOrgnummer = value;
                        break;
                    case "Borettslag-andelsnummer":
                        matrikkelInfo.BorettslagAndelsnummer = value;
                        break;
                }
            }
        }

        return matrikkelInfo;
    }
}