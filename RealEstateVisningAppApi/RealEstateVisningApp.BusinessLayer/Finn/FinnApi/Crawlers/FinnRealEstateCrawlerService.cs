using System.Text.Json;
using Microsoft.Extensions.Logging;
using Polly;
using Polly.Extensions.Http;
using RealEstateVisningApp.BusinessLayer.Finn.CrawlFinn.Mapper;
using RealEstateVisningApp.BusinessLayer.Finn.CrawlFinn.Model;
using RealEstateVisningApp.Database.Repository;

namespace RealEstateVisningApp.BusinessLayer.Finn.FinnApi.Crawlers;

public class FinnRealEstateCrawlerService
{
    private readonly HttpClient _httpClient;
    private readonly IAsyncPolicy<HttpResponseMessage> _retryPolicy;
    private readonly IRealEstateRepository _realEstateRepository;
    private readonly IRealEstateMapper _realEstateMapper;
    private readonly ILogger<FinnRealEstateCrawlerService> _logger;
    private readonly string _baseUrl = "https://www.finn.no/api/search-qf?searchkey=SEARCH_ID_REALESTATE_HOMES&vertical=realestate";
    public FinnRealEstateCrawlerService(HttpClient httpClient, IAsyncPolicy<HttpResponseMessage> retryPolicy, IRealEstateRepository realEstateRepository,
        IRealEstateMapper realEstateMapper,
        ILogger<FinnRealEstateCrawlerService> logger
        )
    {
        _httpClient = httpClient;
        _retryPolicy = retryPolicy;
        _realEstateRepository = realEstateRepository;
        _realEstateMapper = realEstateMapper;
        _logger = logger;
    }
    
    

    public async Task<FinnModel> FetchNewest()
    {
        return await FetchListingsInternal();
    }

    public async Task<FinnModel> FetchSpecific(string location, int page = 1)
    {
        return await FetchListingsInternal(location, page);
    }

    private async Task<FinnModel> FetchListingsInternal(string location = "", int page = 1)
    {
        string content = "";
        string url = "";
        try
        {
            url = _baseUrl;
            url += $"&page={page}";
            if (!string.IsNullOrEmpty(location))
            {
                url += $"&location={location}";
            }
        
            var response = await _retryPolicy.ExecuteAsync(() => _httpClient.GetAsync(url));
        
            response.EnsureSuccessStatusCode();

            content = await response.Content.ReadAsStringAsync();

            if (content.Contains("An error occurred while searching"))
            {
                throw new Exception("An error occurred while searching");
            }
        
            var finnModel = JsonSerializer.Deserialize<FinnModel>(content, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
        
            return finnModel;
        }
        catch (Exception e)
        {
            _logger.LogError(e, $"url: {url}, content:  {content}");
            Console.WriteLine(e);
            throw;
        }
        
    }
}