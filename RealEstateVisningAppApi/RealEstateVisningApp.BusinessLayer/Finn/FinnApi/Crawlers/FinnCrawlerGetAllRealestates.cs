// using System.Text.Json;
// using Polly;
// using RealEstateVisningApp.BusinessLayer.Finn.CrawlFinn.Mapper;
// using RealEstateVisningApp.BusinessLayer.Finn.CrawlFinn.Model;
// using RealEstateVisningApp.Database.Repository;
//
// namespace RealEstateVisningApp.BusinessLayer.Finn.FinnApi.Crawlers;
//
// public class FinnCrawlerGetAllRealestates
// {
//     private readonly HttpClient _httpClient;
//     private readonly IAsyncPolicy<HttpResponseMessage> _retryPolicy;
//     private readonly IRealEstateRepository _realEstateRepository;
//     private readonly IRealEstateMapper _realEstateMapper;
//
//     public FinnCrawlerGetAllRealestates(HttpClient httpClient, IAsyncPolicy<HttpResponseMessage> retryPolicy, IRealEstateRepository realEstateRepository,
//         IRealEstateMapper realEstateMapper)
//     {
//         _httpClient = httpClient;
//         _retryPolicy = retryPolicy;
//         _realEstateRepository = realEstateRepository;
//         _realEstateMapper = realEstateMapper;
//     }
//     
//     public async Task FetchAllLocations()
//     {
//         foreach (var location in LocationHierarchy.Locations)
//         {
//             await FetchLocationData(location);
//         }
//     }
//
//     public async Task<FinnModel> FetchLocationData(LocationHierarchy.Location location)
//     {
//
//         var baseUrl = "https://www.finn.no/api/search-qf?searchkey=SEARCH_ID_REALESTATE_HOMES&vertical=realestate";
//         var page = 1;
//         var maxPages = 150;
//
//         while (page <= maxPages)
//         {
//             var url = $"{baseUrl}&location={location.Id}&page={page}";
//             var response = await _retryPolicy.ExecuteAsync(() => _httpClient.GetAsync(url));
//
//             
//             if (!response.IsSuccessStatusCode)
//             {
//                 Console.WriteLine($"Failed to fetch data for location {location.Name} on page {page} after 3 retries.");
//                 break;
//             }
//
//             var content = await response.Content.ReadAsStringAsync();
//             
//             var finnModel = JsonSerializer.Deserialize<FinnModel>(content, new JsonSerializerOptions
//             {
//                 PropertyNamingPolicy = JsonNamingPolicy.CamelCase
//             });
//
//             if (finnModel.Docs.Count == 0 || finnModel.Metadata.IsEndOfPaging)
//             {
//                 break;
//             }
//             page++;
//         }
//     }
// }
//
//
// public class LocationHierarchy
// {
//     public class Location
//     {
//         public string Id { get; set; }
//         public string Name { get; set; }
//         public int Count { get; set; }
//         public List<Location> Sublocations { get; set; } = new List<Location>();
//     }
//
//     public static List<Location> Locations { get; } = new List<Location>
//     {
//         new Location
//         {
//             Id = "0.22042",
//             Name = "Agder",
//             Count = 2101,
//             Sublocations = new List<Location>
//             {
//                 new Location
//                 {
//                     Id = "1.22042.20166",
//                     Name = "Arendal",
//                     Count = 279,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22042.20172",
//                     Name = "Birkenes",
//                     Count = 20,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22042.20176",
//                     Name = "Bygland",
//                     Count = 3,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22042.20178",
//                     Name = "Bykle",
//                     Count = 6,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22042.20175",
//                     Name = "Evje og Hornnes",
//                     Count = 13,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22042.20181",
//                     Name = "Farsund",
//                     Count = 70,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22042.20182",
//                     Name = "Flekkefjord",
//                     Count = 46,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22042.20170",
//                     Name = "Froland",
//                     Count = 37,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22042.20167",
//                     Name = "Gjerstad",
//                     Count = 8,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22042.20165",
//                     Name = "Grimstad",
//                     Count = 133,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22042.20191",
//                     Name = "Hægebostad",
//                     Count = 9,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22042.20174",
//                     Name = "Iveland",
//                     Count = 4,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22042.20179",
//                     Name = "Kristiansand",
//                     Count = 919,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22042.20192",
//                     Name = "Kvinesdal",
//                     Count = 19,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22042.20171",
//                     Name = "Lillesand",
//                     Count = 107,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22042.20189",
//                     Name = "Lindesnes",
//                     Count = 93,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22042.20190",
//                     Name = "Lyngdal",
//                     Count = 54,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22042.20164",
//                     Name = "Risør",
//                     Count = 104,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22042.20193",
//                     Name = "Sirdal",
//                     Count = 11,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22042.20169",
//                     Name = "Tvedestrand",
//                     Count = 72,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22042.20177",
//                     Name = "Valle",
//                     Count = 4,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22042.20168",
//                     Name = "Vegårshei",
//                     Count = 16,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22042.20183",
//                     Name = "Vennesla",
//                     Count = 68,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22042.20173",
//                     Name = "Åmli",
//                     Count = 8,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22042.20187",
//                     Name = "Åseral",
//                     Count = 1,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//             }
//         },
//         new Location
//         {
//             Id = "0.20003",
//             Name = "Akershus",
//             Count = 5506,
//             Sublocations = new List<Location>
//             {
//                 new Location
//                 {
//                     Id = "1.20003.20046",
//                     Name = "Asker",
//                     Count = 589,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20003.20047",
//                     Name = "Aurskog-Høland",
//                     Count = 213,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20003.20045",
//                     Name = "Bærum",
//                     Count = 588,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20003.20058",
//                     Name = "Eidsvoll",
//                     Count = 238,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20003.20051",
//                     Name = "Enebakk",
//                     Count = 90,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20003.20042",
//                     Name = "Frogn - Drøbak",
//                     Count = 114,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20003.20055",
//                     Name = "Gjerdrum",
//                     Count = 35,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20003.20060",
//                     Name = "Hurdal",
//                     Count = 20,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20003.20099",
//                     Name = "Jevnaker",
//                     Count = 33,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20003.22105",
//                     Name = "Lillestrøm",
//                     Count = 607,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20003.20100",
//                     Name = "Lunner",
//                     Count = 28,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20003.20052",
//                     Name = "Lørenskog",
//                     Count = 598,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20003.20059",
//                     Name = "0nestad",
//                     Count = 145,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20003.20057",
//                     Name = "Nes",
//                     Count = 136,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20003.20043",
//                     Name = "Nesodden",
//                     Count = 230,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20003.20054",
//                     Name = "Nittedal",
//                     Count = 85,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20003.22104",
//                     Name = "Nordre Follo",
//                     Count = 504,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20003.20050",
//                     Name = "Rælingen",
//                     Count = 105,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20003.20056",
//                     Name = "Ullensaker",
//                     Count = 526,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20003.20039",
//                     Name = "Vestby",
//                     Count = 280,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20003.20041",
//                     Name = "Ås",
//                     Count = 342,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//             }
//         },
//         new Location
//         {
//             Id = "0.20007",
//             Name = "Buskerud",
//             Count = 1673,
//             Sublocations = new List<Location>
//             {
//                 new Location
//                 {
//                     Id = "1.20007.20110",
//                     Name = "Drammen",
//                     Count = 745,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20007.20128",
//                     Name = "Flesberg",
//                     Count = 5,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20007.20114",
//                     Name = "Flå",
//                     Count = 9,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20007.20116",
//                     Name = "Gol",
//                     Count = 22,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20007.20117",
//                     Name = "Hemsedal",
//                     Count = 26,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20007.20119",
//                     Name = "Hol",
//                     Count = 45,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20007.20113",
//                     Name = "Hole",
//                     Count = 100,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20007.20111",
//                     Name = "Kongsberg",
//                     Count = 180,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20007.20121",
//                     Name = "Krødsherad",
//                     Count = 19,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20007.20125",
//                     Name = "Lier",
//                     Count = 115,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20007.20122",
//                     Name = "Modum",
//                     Count = 79,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20007.20115",
//                     Name = "Nesbyen",
//                     Count = 21,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20007.20130",
//                     Name = "Nore og Uvdal",
//                     Count = 2,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20007.20112",
//                     Name = "Ringerike",
//                     Count = 163,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20007.20129",
//                     Name = "Rollag",
//                     Count = 6,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20007.20120",
//                     Name = "Sigdal",
//                     Count = 34,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20007.20118",
//                     Name = "Ål",
//                     Count = 13,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20007.20123",
//                     Name = "Øvre Eiker",
//                     Count = 98,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//             }
//         },
//         new Location
//         {
//             Id = "0.20020",
//             Name = "Finnmark",
//             Count = 382,
//             Sublocations = new List<Location>
//             {
//                 new Location
//                 {
//                     Id = "1.20020.20441",
//                     Name = "Alta",
//                     Count = 109,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20020.20451",
//                     Name = "Berlevåg",
//                     Count = 8,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20020.20454",
//                     Name = "Båtsfjord",
//                     Count = 4,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20020.20450",
//                     Name = "Gamvik",
//                     Count = 6,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20020.20439",
//                     Name = "Hammerfest",
//                     Count = 43,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20020.20443",
//                     Name = "Hasvik",
//                     Count = 4,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20020.20448",
//                     Name = "Karasjok",
//                     Count = 19,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20020.20440",
//                     Name = "Kautokeino",
//                     Count = 2,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20020.20449",
//                     Name = "Lebesby",
//                     Count = 2,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20020.20442",
//                     Name = "Loppa",
//                     Count = 1,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20020.20445",
//                     Name = "Måsøy",
//                     Count = 2,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20020.20453",
//                     Name = "Nesseby",
//                     Count = 5,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20020.20446",
//                     Name = "Nordkapp",
//                     Count = 19,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20020.20447",
//                     Name = "Porsanger",
//                     Count = 11,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20020.20455",
//                     Name = "Sør-Varanger",
//                     Count = 84,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20020.20452",
//                     Name = "Tana",
//                     Count = 10,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20020.20438",
//                     Name = "Vadsø",
//                     Count = 43,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20020.20437",
//                     Name = "Vardø",
//                     Count = 10,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//             }
//         },
//         new Location
//         {
//             Id = "0.22034",
//             Name = "Innlandet",
//             Count = 2509,
//             Sublocations = new List<Location>
//             {
//                 new Location
//                 {
//                     Id = "1.22034.20081",
//                     Name = "Alvdal",
//                     Count = 11,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20086",
//                     Name = "Dovre",
//                     Count = 4,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20069",
//                     Name = "Eidskog",
//                     Count = 18,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20073",
//                     Name = "Elverum",
//                     Count = 232,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20078",
//                     Name = "Engerdal",
//                     Count = 1,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20105",
//                     Name = "Etnedal",
//                     Count = 1,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20082",
//                     Name = "Folldal",
//                     Count = 6,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20096",
//                     Name = "Gausdal",
//                     Count = 22,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20085",
//                     Name = "Gjøvik",
//                     Count = 268,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20101",
//                     Name = "Gran",
//                     Count = 94,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20070",
//                     Name = "Grue",
//                     Count = 11,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20063",
//                     Name = "Hamar",
//                     Count = 315,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20062",
//                     Name = "Kongsvinger",
//                     Count = 104,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20087",
//                     Name = "Lesja",
//                     Count = 7,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20084",
//                     Name = "Lillehammer",
//                     Count = 228,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20089",
//                     Name = "Lom",
//                     Count = 2,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20065",
//                     Name = "Løten",
//                     Count = 48,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20106",
//                     Name = "Nord-Aurdal",
//                     Count = 11,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20091",
//                     Name = "Nord-Fron",
//                     Count = 25,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20067",
//                     Name = "Nord-Odal",
//                     Count = 14,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20103",
//                     Name = "Nordre Land",
//                     Count = 28,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20083",
//                     Name = "Os (Innlandet)",
//                     Count = 0,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20077",
//                     Name = "Rendalen",
//                     Count = 8,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20094",
//                     Name = "Ringebu",
//                     Count = 19,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20064",
//                     Name = "Ringsaker",
//                     Count = 247,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20092",
//                     Name = "Sel",
//                     Count = 21,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20088",
//                     Name = "Skjåk",
//                     Count = 4,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20066",
//                     Name = "Stange",
//                     Count = 133,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20076",
//                     Name = "Stor-Elvdal",
//                     Count = 13,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20102",
//                     Name = "Søndre Land",
//                     Count = 40,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20104",
//                     Name = "Sør-Aurdal",
//                     Count = 11,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20093",
//                     Name = "Sør-Fron",
//                     Count = 5,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20068",
//                     Name = "Sør-Odal",
//                     Count = 58,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20079",
//                     Name = "Tolga",
//                     Count = 6,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20074",
//                     Name = "Trysil",
//                     Count = 39,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20080",
//                     Name = "Tynset",
//                     Count = 34,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20109",
//                     Name = "Vang",
//                     Count = 2,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20098",
//                     Name = "Vestre Toten",
//                     Count = 118,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20072",
//                     Name = "Våler (Innlandet)",
//                     Count = 0,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20075",
//                     Name = "Åmot",
//                     Count = 51,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20071",
//                     Name = "Åsnes",
//                     Count = 31,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20097",
//                     Name = "Østre Toten",
//                     Count = 164,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20095",
//                     Name = "Øyer",
//                     Count = 24,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22034.20108",
//                     Name = "Øystre Slidre",
//                     Count = 4,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//             }
//         },
//         new Location
//         {
//             Id = "0.20015",
//             Name = "Møre og Romsdal",
//             Count = 1669,
//             Sublocations = new List<Location>
//             {
//                 new Location
//                 {
//                     Id = "1.20015.20304",
//                     Name = "Aukra",
//                     Count = 19,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.20314",
//                     Name = "Aure",
//                     Count = 13,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.20307",
//                     Name = "Averøy",
//                     Count = 41,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.22101",
//                     Name = "Fjord",
//                     Count = 2,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.20297",
//                     Name = "Giske",
//                     Count = 79,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.20309",
//                     Name = "Gjemnes",
//                     Count = 9,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.20298",
//                     Name = "Haram",
//                     Count = 45,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.20287",
//                     Name = "Hareid",
//                     Count = 27,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.20285",
//                     Name = "Herøy (M.R.)",
//                     Count = 0,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.22102",
//                     Name = "Hustadvika",
//                     Count = 96,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.20281",
//                     Name = "Kristiansund",
//                     Count = 136,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.20280",
//                     Name = "Molde",
//                     Count = 161,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.20300",
//                     Name = "Rauma",
//                     Count = 25,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.20284",
//                     Name = "Sande (M.R.)",
//                     Count = 0,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.20317",
//                     Name = "Smøla",
//                     Count = 9,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.20292",
//                     Name = "Stranda",
//                     Count = 17,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.20296",
//                     Name = "Sula",
//                     Count = 34,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.20311",
//                     Name = "Sunndal",
//                     Count = 8,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.20312",
//                     Name = "Surnadal",
//                     Count = 17,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.20294",
//                     Name = "Sykkylven",
//                     Count = 37,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.20310",
//                     Name = "Tingvoll",
//                     Count = 11,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.20286",
//                     Name = "Ulstein",
//                     Count = 74,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.20283",
//                     Name = "Vanylven",
//                     Count = 7,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.20299",
//                     Name = "Vestnes",
//                     Count = 33,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.20288",
//                     Name = "Volda",
//                     Count = 87,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.20282",
//                     Name = "Ålesund",
//                     Count = 551,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20015.20289",
//                     Name = "Ørsta",
//                     Count = 66,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//             }
//         },
//         new Location
//         {
//             Id = "0.20018",
//             Name = "Nordland",
//             Count = 1393,
//             Sublocations = new List<Location>
//             {
//                 new Location
//                 {
//                     Id = "1.20018.20375",
//                     Name = "Alstahaug",
//                     Count = 33,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20410",
//                     Name = "Andøy",
//                     Count = 15,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20389",
//                     Name = "Beiarn",
//                     Count = 2,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20369",
//                     Name = "Bindal",
//                     Count = 3,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20367",
//                     Name = "Bodø",
//                     Count = 428,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20371",
//                     Name = "Brønnøy",
//                     Count = 32,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20407",
//                     Name = "Bø (Nordland)",
//                     Count = 0,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20380",
//                     Name = "Dønna",
//                     Count = 12,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20399",
//                     Name = "Evenes",
//                     Count = 6,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20391",
//                     Name = "Fauske",
//                     Count = 33,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20403",
//                     Name = "Flakstad",
//                     Count = 4,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20388",
//                     Name = "Gildeskål",
//                     Count = 5,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20378",
//                     Name = "Grane",
//                     Count = 4,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20406",
//                     Name = "Hadsel",
//                     Count = 52,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20395",
//                     Name = "Hamarøy",
//                     Count = 5,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20379",
//                     Name = "Hattfjelldal",
//                     Count = 5,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20382",
//                     Name = "Hemnes",
//                     Count = 28,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20374",
//                     Name = "Herøy (Nordland)",
//                     Count = 0,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20376",
//                     Name = "Leirfjord",
//                     Count = 14,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20384",
//                     Name = "Lurøy",
//                     Count = 10,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20397",
//                     Name = "Lødingen",
//                     Count = 15,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20387",
//                     Name = "Meløy",
//                     Count = 35,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20411",
//                     Name = "Moskenes",
//                     Count = 6,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20368",
//                     Name = "Narvik",
//                     Count = 82,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20381",
//                     Name = "Nesna",
//                     Count = 29,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20383",
//                     Name = "Rana",
//                     Count = 179,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20386",
//                     Name = "Rødøy",
//                     Count = 8,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20401",
//                     Name = "Røst",
//                     Count = 2,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20390",
//                     Name = "Saltdal",
//                     Count = 6,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20409",
//                     Name = "Sortland",
//                     Count = 27,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20394",
//                     Name = "Steigen",
//                     Count = 10,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20370",
//                     Name = "Sømna",
//                     Count = 11,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20393",
//                     Name = "Sørfold",
//                     Count = 14,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20385",
//                     Name = "Træna",
//                     Count = 1,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20377",
//                     Name = "Vefsn",
//                     Count = 72,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20372",
//                     Name = "Vega",
//                     Count = 6,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20404",
//                     Name = "Vestvågøy",
//                     Count = 91,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20405",
//                     Name = "Vågan",
//                     Count = 60,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20402",
//                     Name = "Værøy",
//                     Count = 3,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20018.20408",
//                     Name = "Øksnes",
//                     Count = 23,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//             }
//         },
//         new Location
//         {
//             Id = "0.20061",
//             Name = "Oslo",
//             Count = 2823,
//             Sublocations = new List<Location>
//             {
//                 new Location
//                 {
//                     Id = "1.20061.20528",
//                     Name = "Bjerke",
//                     Count = 519,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20507",
//                     Name = "Bygdøy - Frogner",
//                     Count = 110,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20519",
//                     Name = "Bøler",
//                     Count = 29,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20515",
//                     Name = "Ekeberg - Bekkelaget",
//                     Count = 53,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20524",
//                     Name = "Furuset",
//                     Count = 55,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20512",
//                     Name = "Gamle Oslo",
//                     Count = 249,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20529",
//                     Name = "Grefsen - Kjelsås",
//                     Count = 133,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20527",
//                     Name = "Grorud",
//                     Count = 50,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20511",
//                     Name = "Grünerløkka - Sofienberg",
//                     Count = 350,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20523",
//                     Name = "Hellerud",
//                     Count = 41,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20522",
//                     Name = "Helsfyr - Sinsen",
//                     Count = 163,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20518",
//                     Name = "Lambertseter",
//                     Count = 29,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20520",
//                     Name = "Manglerud",
//                     Count = 24,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20514",
//                     Name = "Marka",
//                     Count = 2,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20516",
//                     Name = "Nordstrand",
//                     Count = 118,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20526",
//                     Name = "Romsås",
//                     Count = 8,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20532",
//                     Name = "Røa",
//                     Count = 120,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20510",
//                     Name = "Sagene - Torshov",
//                     Count = 256,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20513",
//                     Name = "Sentrum",
//                     Count = 13,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20530",
//                     Name = "Sogn",
//                     Count = 27,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20509",
//                     Name = "St.Hanshaugen - Ullevål",
//                     Count = 134,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20525",
//                     Name = "Stovner",
//                     Count = 41,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20517",
//                     Name = "Søndre Nordstrand",
//                     Count = 56,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20533",
//                     Name = "Ullern",
//                     Count = 144,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20508",
//                     Name = "Uranienborg - Majorstuen",
//                     Count = 131,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20531",
//                     Name = "Vinderen",
//                     Count = 99,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20061.20521",
//                     Name = "Østensjø",
//                     Count = 47,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//             }
//         },
//         new Location
//         {
//             Id = "0.20012",
//             Name = "Rogaland",
//             Count = 2525,
//             Sublocations = new List<Location>
//             {
//                 new Location
//                 {
//                     Id = "1.20012.20200",
//                     Name = "Bjerkreim",
//                     Count = 12,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20012.20215",
//                     Name = "Bokn",
//                     Count = 7,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20012.20194",
//                     Name = "Eigersund",
//                     Count = 135,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20012.20204",
//                     Name = "Gjesdal",
//                     Count = 28,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20012.20197",
//                     Name = "Haugesund",
//                     Count = 210,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20012.20209",
//                     Name = "Hjelmeland",
//                     Count = 26,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20012.20201",
//                     Name = "Hå",
//                     Count = 105,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20012.20217",
//                     Name = "Karmøy",
//                     Count = 274,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20012.20202",
//                     Name = "Klepp",
//                     Count = 107,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20012.20214",
//                     Name = "Kvitsøy",
//                     Count = 14,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20012.20199",
//                     Name = "Lund",
//                     Count = 8,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20012.20206",
//                     Name = "Randaberg",
//                     Count = 24,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20012.20195",
//                     Name = "Sandnes",
//                     Count = 366,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20012.20211",
//                     Name = "Sauda",
//                     Count = 16,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20012.20198",
//                     Name = "Sokndal",
//                     Count = 12,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20012.20205",
//                     Name = "Sola",
//                     Count = 87,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20012.20196",
//                     Name = "Stavanger",
//                     Count = 658,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20012.20208",
//                     Name = "Strand",
//                     Count = 79,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20012.20210",
//                     Name = "Suldal",
//                     Count = 17,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20012.20203",
//                     Name = "Time",
//                     Count = 168,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20012.20216",
//                     Name = "Tysvær",
//                     Count = 124,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20012.20219",
//                     Name = "Vindafjord",
//                     Count = 48,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//             }
//         },
//         new Location
//         {
//             Id = "0.20009",
//             Name = "Telemark",
//             Count = 926,
//             Sublocations = new List<Location>
//             {
//                 new Location
//                 {
//                     Id = "1.20009.20150",
//                     Name = "Bamble",
//                     Count = 75,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20009.20152",
//                     Name = "Drangedal",
//                     Count = 8,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20009.20161",
//                     Name = "Fyresdal",
//                     Count = 1,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20009.20157",
//                     Name = "Hjartdal",
//                     Count = 5,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20009.20151",
//                     Name = "Kragerø",
//                     Count = 36,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20009.20159",
//                     Name = "Kviteseid",
//                     Count = 12,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20009.22106",
//                     Name = "Midt-Telemark",
//                     Count = 73,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20009.20160",
//                     Name = "Nissedal",
//                     Count = 7,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20009.20153",
//                     Name = "Nome",
//                     Count = 20,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20009.20148",
//                     Name = "Notodden",
//                     Count = 53,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20009.20146",
//                     Name = "Porsgrunn",
//                     Count = 230,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20009.20158",
//                     Name = "Seljord",
//                     Count = 15,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20009.20149",
//                     Name = "Siljan",
//                     Count = 11,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20009.20147",
//                     Name = "Skien",
//                     Count = 295,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20009.20156",
//                     Name = "Tinn",
//                     Count = 25,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20009.20162",
//                     Name = "Tokke",
//                     Count = 7,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20009.20163",
//                     Name = "Vinje",
//                     Count = 53,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//             }
//         },
//         new Location
//         {
//             Id = "0.20019",
//             Name = "Troms",
//             Count = 1099,
//             Sublocations = new List<Location>
//             {
//                 new Location
//                 {
//                     Id = "1.20019.20429",
//                     Name = "Balsfjord",
//                     Count = 17,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20019.20420",
//                     Name = "Bardu",
//                     Count = 24,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20019.20424",
//                     Name = "Dyrøy",
//                     Count = 19,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20019.20418",
//                     Name = "Gratangen",
//                     Count = 3,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20019.20412",
//                     Name = "Harstad",
//                     Count = 171,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20019.20417",
//                     Name = "Ibestad",
//                     Count = 7,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20019.20430",
//                     Name = "Karlsøy",
//                     Count = 31,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20019.20414",
//                     Name = "Kvæfjord",
//                     Count = 17,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20019.20436",
//                     Name = "Kvæ0gen",
//                     Count = 8,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20019.20433",
//                     Name = "Kåfjord",
//                     Count = 8,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20019.20419",
//                     Name = "Lavangen",
//                     Count = 3,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20019.20431",
//                     Name = "Lyngen",
//                     Count = 10,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20019.20422",
//                     Name = "Målselv",
//                     Count = 26,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20019.20435",
//                     Name = "Nordreisa",
//                     Count = 29,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20019.20421",
//                     Name = "Salangen",
//                     Count = 6,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20019.22115",
//                     Name = "Senja",
//                     Count = 83,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20019.20434",
//                     Name = "Skjervøy",
//                     Count = 13,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20019.20432",
//                     Name = "Storfjord",
//                     Count = 10,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20019.20423",
//                     Name = "Sørreisa",
//                     Count = 15,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20019.20398",
//                     Name = "Tjeldsund",
//                     Count = 26,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20019.20413",
//                     Name = "Tromsø",
//                     Count = 573,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//             }
//         },
//         new Location
//         {
//             Id = "0.20016",
//             Name = "Trøndelag",
//             Count = 3977,
//             Sublocations = new List<Location>
//             {
//                 new Location
//                 {
//                     Id = "1.20016.20363",
//                     Name = "Flatanger",
//                     Count = 7,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20347",
//                     Name = "Frosta",
//                     Count = 12,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20322",
//                     Name = "Frøya",
//                     Count = 47,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20359",
//                     Name = "Grong",
//                     Count = 13,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.22112",
//                     Name = "Heim",
//                     Count = 28,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20321",
//                     Name = "Hitra",
//                     Count = 49,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20335",
//                     Name = "Holtålen",
//                     Count = 5,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20360",
//                     Name = "Høylandet",
//                     Count = 4,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20354",
//                     Name = "Inderøy",
//                     Count = 42,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20325",
//                     Name = "Indre Fosen",
//                     Count = 43,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20366",
//                     Name = "Leka",
//                     Count = 3,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20349",
//                     Name = "Levanger",
//                     Count = 126,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20356",
//                     Name = "Lierne",
//                     Count = 2,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20340",
//                     Name = "Malvik",
//                     Count = 74,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20337",
//                     Name = "Melhus",
//                     Count = 154,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20345",
//                     Name = "Meråker",
//                     Count = 11,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20336",
//                     Name = "Midtre Gauldal",
//                     Count = 21,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20344",
//                     Name = "Namsos",
//                     Count = 48,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20358",
//                     Name = "Namsskogan",
//                     Count = 6,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.22114",
//                     Name = "Nærøysund",
//                     Count = 123,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20330",
//                     Name = "Oppdal",
//                     Count = 35,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.22113",
//                     Name = "Orkland",
//                     Count = 88,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20329",
//                     Name = "Osen",
//                     Count = 3,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20361",
//                     Name = "Overhalla",
//                     Count = 13,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20331",
//                     Name = "Rennebu",
//                     Count = 9,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20313",
//                     Name = "Rindal",
//                     Count = 7,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20334",
//                     Name = "Røros",
//                     Count = 53,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20341",
//                     Name = "Selbu",
//                     Count = 28,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20338",
//                     Name = "Skaun",
//                     Count = 38,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20355",
//                     Name = "Snåsa",
//                     Count = 12,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20343",
//                     Name = "Steinkjer",
//                     Count = 191,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20346",
//                     Name = "Stjørdal",
//                     Count = 236,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20318",
//                     Name = "Trondheim",
//                     Count = 2272,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20342",
//                     Name = "Tydal",
//                     Count = 4,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20350",
//                     Name = "Verdal",
//                     Count = 89,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20327",
//                     Name = "Åfjord",
//                     Count = 10,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20016.20323",
//                     Name = "Ørland",
//                     Count = 74,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//             }
//         },
//         new Location
//         {
//             Id = "0.20008",
//             Name = "Vestfold",
//             Count = 1788,
//             Sublocations = new List<Location>
//             {
//                 new Location
//                 {
//                     Id = "1.20008.20143",
//                     Name = "Færder",
//                     Count = 189,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20008.20132",
//                     Name = "Holmestrand",
//                     Count = 301,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20008.20131",
//                     Name = "Horten",
//                     Count = 178,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20008.20135",
//                     Name = "Larvik",
//                     Count = 374,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20008.20134",
//                     Name = "Sandefjord",
//                     Count = 387,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20008.20133",
//                     Name = "Tønsberg",
//                     Count = 386,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//             }
//         },
//         new Location
//         {
//             Id = "0.22046",
//             Name = "Vestland",
//             Count = 2870,
//             Sublocations = new List<Location>
//             {
//                 new Location
//                 {
//                     Id = "1.22046.22109",
//                     Name = "Alver",
//                     Count = 205,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20267",
//                     Name = "Askvoll",
//                     Count = 12,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20243",
//                     Name = "Askøy",
//                     Count = 175,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20263",
//                     Name = "Aurland",
//                     Count = 1,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20240",
//                     Name = "Austevoll",
//                     Count = 33,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20251",
//                     Name = "Austrheim",
//                     Count = 4,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20220",
//                     Name = "Bergen",
//                     Count = 976,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.22108",
//                     Name = "Bjørnafjorden",
//                     Count = 134,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20273",
//                     Name = "Bremanger",
//                     Count = 27,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20224",
//                     Name = "Bømlo",
//                     Count = 100,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20232",
//                     Name = "Eidfjord",
//                     Count = 1,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20221",
//                     Name = "Etne",
//                     Count = 20,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20252",
//                     Name = "Fedje",
//                     Count = 1,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20226",
//                     Name = "Fitjar",
//                     Count = 5,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20268",
//                     Name = "Fjaler",
//                     Count = 8,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20278",
//                     Name = "Gloppen",
//                     Count = 30,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20255",
//                     Name = "Gulen",
//                     Count = 2,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20257",
//                     Name = "Hyllestad",
//                     Count = 8,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20258",
//                     Name = "Høyanger",
//                     Count = 23,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.22107",
//                     Name = "Kinn",
//                     Count = 109,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20236",
//                     Name = "Kvam",
//                     Count = 14,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20228",
//                     Name = "Kvinnherad",
//                     Count = 48,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20266",
//                     Name = "Luster",
//                     Count = 24,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20264",
//                     Name = "Lærdal",
//                     Count = 15,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20246",
//                     Name = "Osterøy",
//                     Count = 28,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20262",
//                     Name = "Sogndal",
//                     Count = 80,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20256",
//                     Name = "Solund",
//                     Count = 8,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.22111",
//                     Name = "Stad",
//                     Count = 63,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20225",
//                     Name = "Stord",
//                     Count = 92,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20279",
//                     Name = "Stryn",
//                     Count = 16,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.22110",
//                     Name = "Sunnfjord",
//                     Count = 109,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20223",
//                     Name = "Sveio",
//                     Count = 38,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20227",
//                     Name = "Tysnes",
//                     Count = 21,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20231",
//                     Name = "Ullensvang",
//                     Count = 14,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20233",
//                     Name = "Ulvik",
//                     Count = 3,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20244",
//                     Name = "Vaksdal",
//                     Count = 17,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20259",
//                     Name = "Vik",
//                     Count = 7,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20235",
//                     Name = "Voss",
//                     Count = 53,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20265",
//                     Name = "Årdal",
//                     Count = 51,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.22046.20248",
//                     Name = "Øygarden",
//                     Count = 295,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//             }
//         },
//         new Location
//         {
//             Id = "0.20002",
//             Name = "Østfold",
//             Count = 2213,
//             Sublocations = new List<Location>
//             {
//                 new Location
//                 {
//                     Id = "1.20002.20026",
//                     Name = "Aremark",
//                     Count = 4,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20002.20024",
//                     Name = "Fredrikstad",
//                     Count = 676,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20002.20021",
//                     Name = "Halden",
//                     Count = 198,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20002.20025",
//                     Name = "Hvaler",
//                     Count = 16,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20002.22103",
//                     Name = "Indre Østfold",
//                     Count = 307,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20002.20027",
//                     Name = "Marker",
//                     Count = 14,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20002.20022",
//                     Name = "Moss",
//                     Count = 388,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20002.20034",
//                     Name = "Rakkestad",
//                     Count = 53,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20002.20035",
//                     Name = "Råde",
//                     Count = 38,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20002.20023",
//                     Name = "Sarpsborg",
//                     Count = 421,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20002.20033",
//                     Name = "Skiptvet",
//                     Count = 14,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//                 new Location
//                 {
//                     Id = "1.20002.20037",
//                     Name = "Våler (Østfold)",
//                     Count = 0,
//                     Sublocations = new List<Location>
//                     {
//                     }
//                 },
//             }
//         }
//     };
// }