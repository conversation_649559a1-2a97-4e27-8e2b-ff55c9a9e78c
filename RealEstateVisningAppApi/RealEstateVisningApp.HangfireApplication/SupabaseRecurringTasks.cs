using Hangfire;
using RealEstateVisningApp.BusinessLayer;

namespace RealEstateVisningApp.HangfireApplication;

public class SupabaseRecurringTasks
{
    public static void ScheduleKeepAliveJob(IServiceProvider services)
    {
        using (var scope = services.CreateScope())
        {
            var jobClient = scope.ServiceProvider.GetRequiredService<IRecurringJobManager>();
            jobClient.AddOrUpdate<SupabaseKeepAliveService>(
                "supabase-keep-alive", // This is the job ID, which must be unique
                job => job.KeepAliveAsync(),
                "*/5 * * * *"
            );
        }
    }
}