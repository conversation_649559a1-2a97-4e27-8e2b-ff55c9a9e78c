using Hangfire;
using Hangfire.Console;
using Hangfire.MemoryStorage;
using Microsoft.EntityFrameworkCore;
using Polly;
using RealEstateVisningApp.BusinessLayer.Finn.CrawlFinn;
using RealEstateVisningApp.BusinessLayer.Finn.CrawlFinn.Mapper;
using RealEstateVisningApp.BusinessLayer.Finn.FinnApi.Crawlers;
using RealEstateVisningApp.Database;
using RealEstateVisningApp.Database.Repository;
using Hangfire.PostgreSql;
using Hangfire.Storage;
using RealEstateVisningApp.BusinessLayer;
using Serilog;
using Serilog.Events;

namespace RealEstateVisningApp.HangfireApplication;

public class Program
{
    public static IConfiguration Configuration; 
    public static void Main(string[] args)
    {
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
            .Enrich.FromLogContext()
            .WriteTo.Console()
            .WriteTo.File("logs/Hangfire_logs.txt", rollingInterval: RollingInterval.Day)
            .CreateLogger();
        
        var builder = WebApplication.CreateBuilder(args);
        
        Configuration = builder.Configuration;
        
        Log.Logger.Information("Appsettings Environment {0}", Configuration.GetValue<string>("AppSettingsEnvironment"));
        
        
        builder.Host.UseSerilog(); // Use Serilog for logging
        
        builder.Services.AddSingleton<SupabaseService>();
        // Add services to the container.
        builder.Services.AddAuthorization();
       
        // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
        builder.Services.AddEndpointsApiExplorer();
        builder.Services.AddSwaggerGen();

        AddDependencyInjection(builder);
        
        SetupHangfire(builder);
        
        var app = builder.Build();
        
        RemoveAllRecurringJobs(app.Services);
        
        // Configure the HTTP request pipeline.
        if (app.Environment.IsDevelopment())
        {
            app.UseSwagger();
            app.UseSwaggerUI();
        }
        app.UseHttpsRedirection();
        app.UseAuthorization();
        app.MapHangfireDashboard();
        
        ScheduleJob(app.Services);
        
        app.Run();
    }
    static void AddDependencyInjection( WebApplicationBuilder builder)
    {
        builder.Services.AddDbContext<RealEstateDbContext>(options =>
            options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));

        builder.Services.AddScoped<IRealEstateRepository, RealEstateRepository>();   
        builder.Services.AddScoped<FinnRealEstateService>();   
        builder.Services.AddHttpClient<FinnRealEstateCrawlerService>(); // services.AddHttpClient();
                                                                        
        builder.Services.AddSingleton<IAsyncPolicy<HttpResponseMessage>>(GetRetryPolicy());
        builder.Services.AddScoped<IRealEstateMapper, RealEstateMapper>();
    }
    static void SetupHangfire( WebApplicationBuilder builder)
    {
        if (Configuration.GetValue<bool>("Hangfire:UseInMemoryStorage"))
        {
            builder.Services.AddHangfire((serviceProvider, configuration) => configuration
                .SetDataCompatibilityLevel(CompatibilityLevel.Version_170)
                .UseSimpleAssemblyNameTypeSerializer()
                .UseRecommendedSerializerSettings()
                .UseConsole()
                .UseMemoryStorage());
        }
        else
        {
            builder.Services.AddHangfire((serviceProvider, configuration) => configuration
                .SetDataCompatibilityLevel(CompatibilityLevel.Version_170)
                .UseSimpleAssemblyNameTypeSerializer()
                .UseRecommendedSerializerSettings()
                .UseConsole()
                .UsePostgreSqlStorage(builder.Configuration.GetConnectionString("DefaultConnection")));
        }
        
        builder.Services.AddHangfireServer();
    }
    static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy()
    {
        return Policy<HttpResponseMessage>
            .Handle<HttpRequestException>()
            .OrResult(r => r.StatusCode == System.Net.HttpStatusCode.RequestTimeout ||
                           r.StatusCode == (System.Net.HttpStatusCode)429 || // Too Many Requests
                           r.StatusCode == System.Net.HttpStatusCode.InternalServerError ||
                           r.StatusCode == System.Net.HttpStatusCode.BadGateway ||
                           r.StatusCode == System.Net.HttpStatusCode.ServiceUnavailable ||
                           r.StatusCode == System.Net.HttpStatusCode.GatewayTimeout ||
                           r.StatusCode == System.Net.HttpStatusCode.BadRequest)  // Bad Request
            .WaitAndRetryAsync(3, retryAttempt => TimeSpan.FromSeconds(2),
                onRetry: (outcome, timespan, retryAttempt, context) =>
                {
                    Console.WriteLine($"Retrying... Attempt {retryAttempt}");
                    
                });
    }
    static void ScheduleJob(IServiceProvider services)
    {
        
        SupabaseRecurringTasks.ScheduleKeepAliveJob(services);
        
        using (var scope = services.CreateScope())
        {
            var runFinnRealEstateService = Configuration.GetValue<bool>("Hangfire:BackgroundJobs:UpdateNewestRealEstates:IsEnabled");
            
            var jobClient = scope.ServiceProvider.GetRequiredService<IRecurringJobManager>();
            if (runFinnRealEstateService)
            {
                jobClient.AddOrUpdate<FinnRealEstateService>(
                    "finn-real-estate-fetch-recurring", // This is the job ID, which must be unique
                    job => job.UpdateNewestRealEstateFromFinn(null),
                    Configuration.GetValue<string>("Hangfire:BackgroundJobs:UpdateNewestRealEstates:Cron") // This cron expression schedules the job to run every 10 minutes
                );
            }
            
            var runUpdateAllRealEstates = Configuration.GetValue<bool>("Hangfire:BackgroundJobs:UpdateAllRealEstates:IsEnabled");
            if (runUpdateAllRealEstates)
            {
                var locations = new List<LocationHierarchy.Location> {
                    new LocationHierarchy.Location
                    {
                        Id = "1.22042.20172",
                        Name = "Birkenes",
                        Count = 20,
                    }
                };
                var cron = Configuration.GetValue<string>("Hangfire:BackgroundJobs:UpdateAllRealEstates:Cron");
                jobClient.AddOrUpdate<FinnRealEstateService>(
                    "update-all-real-estates", // This is the job ID, which must be unique
                    job => job.AddOrUpdateAllRealEstateFromFinn(null, LocationHierarchy.Locations),
                    cron
                    // This cron expression schedules the job to run every 10 minutes
                );
            }
        }
    }
    static void RemoveAllRecurringJobs(IServiceProvider services)
    {
        if (Configuration.GetValue<string>("Hangfire:RemoveAllRecurringJobs") != "true")
        {
            return;
        }
        using var scope = services.CreateScope();
        var recurringJobManager = scope.ServiceProvider.GetRequiredService<IRecurringJobManager>();
        var connection = scope.ServiceProvider.GetRequiredService<JobStorage>().GetConnection();
        var recurringJobs = connection.GetRecurringJobs();

        foreach (var job in recurringJobs)
        {
            recurringJobManager.RemoveIfExists(job.Id);
        }
    }
}