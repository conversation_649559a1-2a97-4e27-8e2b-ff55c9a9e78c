using Hangfire.Logging;
using LogLevel = Microsoft.Extensions.Logging.LogLevel;

namespace RealEstateVisningApp.HangfireApplication;

public class AspNetCoreLogProvider : ILogProvider
    {
        private readonly ILoggerFactory _loggerFactory;

        public AspNetCoreLogProvider(ILoggerFactory loggerFactory)
        {
            _loggerFactory = loggerFactory;
        }

        public ILog GetLogger(string name)
        {
            return new AspNetCoreLog(_loggerFactory.CreateLogger(name));
        }
    }

    public class AspNetCoreLog  : Hangfire.Logging.ILog
    {
        private readonly ILogger _logger;

        public AspNetCoreLog(ILogger logger)
        {
            _logger = logger;
        }

        public bool Log(LogLevel logLevel, Func<string> messageFunc, Exception exception = null)
        {
            if (messageFunc == null)
            {
                return _logger.IsEnabled(ToMicrosoftLogLevel(logLevel));
            }

            _logger.Log(ToMicrosoftLogLevel(logLevel), exception, messageFunc());
            return true;
        }

        private static LogLevel ToMicrosoftLogLevel(LogLevel logLevel)
        {
            switch (logLevel)
            {
                case LogLevel.Trace:
                    return Microsoft.Extensions.Logging.LogLevel.Trace;
                case LogLevel.Debug:
                    return Microsoft.Extensions.Logging.LogLevel.Debug;
                case LogLevel.Information:
                    return Microsoft.Extensions.Logging.LogLevel.Information;
                case LogLevel.Warning:
                    return Microsoft.Extensions.Logging.LogLevel.Warning;
                case LogLevel.Error:
                    return Microsoft.Extensions.Logging.LogLevel.Error;
                case LogLevel.Critical:
                    return Microsoft.Extensions.Logging.LogLevel.Critical;
                default:
                    return Microsoft.Extensions.Logging.LogLevel.None;
            }
        }
        
        private static Microsoft.Extensions.Logging.LogLevel ToMicrosoftLogLevel(Hangfire.Logging.LogLevel logLevel)
        {
            switch (logLevel)
            {
                case Hangfire.Logging.LogLevel.Trace:
                    return Microsoft.Extensions.Logging.LogLevel.Trace;
                case Hangfire.Logging.LogLevel.Debug:
                    return Microsoft.Extensions.Logging.LogLevel.Debug;
                case Hangfire.Logging.LogLevel.Info:
                    return Microsoft.Extensions.Logging.LogLevel.Information;
                case Hangfire.Logging.LogLevel.Warn:
                    return Microsoft.Extensions.Logging.LogLevel.Warning;
                case Hangfire.Logging.LogLevel.Error:
                    return Microsoft.Extensions.Logging.LogLevel.Error;
                case Hangfire.Logging.LogLevel.Fatal:
                    return Microsoft.Extensions.Logging.LogLevel.Critical;
                default:
                    return Microsoft.Extensions.Logging.LogLevel.None;
            }
        }

        public bool Log(Hangfire.Logging.LogLevel logLevel, Func<string> messageFunc, Exception exception = null)
        {
            var microsoftLogLevel = ToMicrosoftLogLevel(logLevel);

            if (messageFunc == null)
            {
                return _logger.IsEnabled(microsoftLogLevel);
            }

            _logger.Log(microsoftLogLevel, exception, messageFunc());
            return true;
        }
    }