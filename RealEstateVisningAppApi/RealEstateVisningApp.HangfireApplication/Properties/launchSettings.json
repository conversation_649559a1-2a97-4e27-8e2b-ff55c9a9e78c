{"$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:47562", "sslPort": 44364}}, "profiles": {"http": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "hangfire", "applicationUrl": "http://localhost:5208", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "hangfire", "applicationUrl": "https://localhost:7110;http://localhost:5208", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}