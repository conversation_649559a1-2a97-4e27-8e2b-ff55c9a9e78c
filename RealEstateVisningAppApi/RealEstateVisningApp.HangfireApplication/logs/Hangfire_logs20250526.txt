2025-05-26 00:00:02.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:00:02.290 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:00:02.291 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:00:02.291 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 8.3377ms
2025-05-26 00:00:05.281 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:00:05.282 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:00:05.282 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:00:05.283 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.4256ms
2025-05-26 00:00:06.829 +02:00 [DBG] Server nomll7y29f004:1590:408fe5e8 heartbeat successfully sent
2025-05-26 00:00:08.285 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:00:08.286 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:00:08.288 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:00:08.289 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.226ms
2025-05-26 00:00:11.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:00:11.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:00:11.289 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:00:11.289 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.748ms
2025-05-26 00:00:11.352 +02:00 [DBG] 2 recurring job(s) processed by scheduler.
2025-05-26 00:00:11.439 +02:00 [INF] Start processing HTTP request GET https://www.finn.no/api/search-qf?searchkey=SEARCH_ID_REALESTATE_HOMES&vertical=realestate&page=1
2025-05-26 00:00:11.470 +02:00 [INF] Sending HTTP request GET https://www.finn.no/api/search-qf?searchkey=SEARCH_ID_REALESTATE_HOMES&vertical=realestate&page=1
2025-05-26 00:00:11.787 +02:00 [INF] Received HTTP response headers after 317.0122ms - 200
2025-05-26 00:00:11.788 +02:00 [INF] End processing HTTP request after 377.7914ms - 200
2025-05-26 00:00:12.191 +02:00 [INF] Executed DbCommand (33ms) [Parameters=[@__apiIds_0='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
SELECT r.finn_ad_id
FROM real_estate_listing AS r
WHERE r.finn_ad_id = ANY (@__apiIds_0)
2025-05-26 00:00:12.191 +02:00 [INF] No new listings to add to the database.
2025-05-26 00:00:14.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:00:14.282 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:00:14.283 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:00:14.283 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.9184ms
2025-05-26 00:00:17.287 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:00:17.288 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:00:17.289 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:00:17.289 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.611ms
2025-05-26 00:00:20.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:00:20.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:00:20.284 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:00:20.285 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.0414ms
2025-05-26 00:00:23.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:00:23.286 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:00:23.288 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:00:23.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.3899ms
2025-05-26 00:00:26.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:00:26.282 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:00:26.283 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:00:26.283 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.7392ms
2025-05-26 00:00:29.287 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:00:29.288 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:00:29.289 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:00:29.289 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.5629ms
2025-05-26 00:00:32.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:00:32.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:00:32.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:00:32.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.8602ms
2025-05-26 00:00:35.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:00:35.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:00:35.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:00:35.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.7141ms
2025-05-26 00:00:36.830 +02:00 [DBG] Server nomll7y29f004:1590:408fe5e8 heartbeat successfully sent
2025-05-26 00:00:38.286 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:00:38.287 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:00:38.289 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:00:38.289 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.4568ms
2025-05-26 00:00:41.281 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:00:41.282 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:00:41.282 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:00:41.282 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.8389ms
2025-05-26 00:00:44.285 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:00:44.286 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:00:44.289 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:00:44.290 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 5.2592ms
2025-05-26 00:00:47.285 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:00:47.289 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:00:47.290 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:00:47.291 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 5.8285ms
2025-05-26 00:00:50.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:00:50.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:00:50.289 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:00:50.289 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 5.1578ms
2025-05-26 00:00:53.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:00:53.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:00:53.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:00:53.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.7002ms
2025-05-26 00:00:56.280 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:00:56.281 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:00:56.281 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:00:56.281 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.7024ms
2025-05-26 00:00:59.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:00:59.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:00:59.288 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:00:59.289 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.7099ms
2025-05-26 00:01:02.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:01:02.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:01:02.289 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:01:02.289 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 5.1112ms
2025-05-26 00:01:05.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:01:05.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:01:05.288 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:01:05.289 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 5.4369ms
2025-05-26 00:01:06.832 +02:00 [DBG] Server nomll7y29f004:1590:408fe5e8 heartbeat successfully sent
2025-05-26 00:01:08.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:01:08.282 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:01:08.283 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:01:08.283 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.0937ms
2025-05-26 00:01:11.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:01:11.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:01:11.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:01:11.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.0555ms
2025-05-26 00:01:14.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:01:14.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:01:14.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:01:14.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.2047ms
2025-05-26 00:01:17.287 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:01:17.288 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:01:17.289 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:01:17.292 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 5.1697ms
2025-05-26 00:01:20.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:01:20.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:01:20.288 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:01:20.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.8317ms
2025-05-26 00:01:23.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:01:23.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:01:23.285 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:01:23.285 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.8997ms
2025-05-26 00:01:26.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:01:26.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:01:26.285 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:01:26.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.8548ms
2025-05-26 00:01:29.285 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:01:29.287 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:01:29.289 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:01:29.290 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.486ms
2025-05-26 00:01:32.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:01:32.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:01:32.289 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:01:32.289 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.7283ms
2025-05-26 00:01:35.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:01:35.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:01:35.285 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:01:35.285 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.1122ms
2025-05-26 00:01:36.833 +02:00 [DBG] Server nomll7y29f004:1590:408fe5e8 heartbeat successfully sent
2025-05-26 00:01:38.280 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:01:38.280 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:01:38.280 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:01:38.281 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.7535ms
2025-05-26 00:01:41.285 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:01:41.286 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:01:41.288 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:01:41.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.297ms
2025-05-26 00:01:44.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:01:44.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:01:44.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:01:44.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.9405ms
2025-05-26 00:01:47.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:01:47.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:01:47.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:01:47.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.4611ms
2025-05-26 00:01:50.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:01:50.282 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:01:50.282 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:01:50.283 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.024ms
2025-05-26 00:01:53.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:01:53.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:01:53.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:01:53.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.9983ms
2025-05-26 00:01:56.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:01:56.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:01:56.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:01:56.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.5809ms
2025-05-26 00:01:59.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:01:59.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:01:59.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:01:59.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 5.1554ms
2025-05-26 00:02:02.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:02:02.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:02:02.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:02:02.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.4726ms
2025-05-26 00:02:05.281 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:02:05.282 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:02:05.282 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:02:05.282 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.7482ms
2025-05-26 00:02:06.835 +02:00 [DBG] Server nomll7y29f004:1590:408fe5e8 heartbeat successfully sent
2025-05-26 00:02:08.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:02:08.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:02:08.289 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:02:08.290 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 6.023ms
2025-05-26 00:02:11.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:02:11.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:02:11.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:02:11.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.3882ms
2025-05-26 00:02:14.285 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:02:14.286 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:02:14.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:02:14.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.847ms
2025-05-26 00:02:17.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:02:17.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:02:17.284 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:02:17.284 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.3606ms
2025-05-26 00:02:20.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:02:20.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:02:20.283 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:02:20.283 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.8392ms
2025-05-26 00:02:23.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:02:23.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:02:23.288 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:02:23.289 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.6013ms
2025-05-26 00:02:26.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:02:26.287 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:02:26.289 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:02:26.289 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.6666ms
2025-05-26 00:02:29.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:02:29.286 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:02:29.289 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:02:29.289 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 5.0954ms
2025-05-26 00:02:32.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:02:32.282 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:02:32.282 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:02:32.282 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.9157ms
2025-05-26 00:02:35.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:02:35.286 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:02:35.289 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:02:35.289 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 5.259ms
2025-05-26 00:02:36.837 +02:00 [DBG] Server nomll7y29f004:1590:408fe5e8 heartbeat successfully sent
2025-05-26 00:02:38.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:02:38.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:02:38.288 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:02:38.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.7267ms
2025-05-26 00:02:41.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:02:41.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:02:41.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:02:41.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.0845ms
2025-05-26 00:02:44.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:02:44.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:02:44.288 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:02:44.289 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 5.3667ms
2025-05-26 00:02:47.281 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:02:47.282 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:02:47.282 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:02:47.282 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.7022ms
2025-05-26 00:02:50.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:02:50.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:02:50.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:02:50.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.0351ms
2025-05-26 00:02:53.285 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:02:53.286 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:02:53.288 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:02:53.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.513ms
2025-05-26 00:02:56.285 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:02:56.286 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:02:56.288 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:02:56.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.3318ms
2025-05-26 00:02:59.281 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:02:59.282 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:02:59.283 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:02:59.283 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.329ms
2025-05-26 00:03:02.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:03:02.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:03:02.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:03:02.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.7858ms
2025-05-26 00:03:05.281 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:03:05.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:03:05.285 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:03:05.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.7181ms
2025-05-26 00:03:06.839 +02:00 [DBG] Server nomll7y29f004:1590:408fe5e8 heartbeat successfully sent
2025-05-26 00:03:08.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:03:08.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:03:08.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:03:08.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.7946ms
2025-05-26 00:03:11.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:03:11.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:03:11.284 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:03:11.285 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.1342ms
2025-05-26 00:03:14.285 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:03:14.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:03:14.285 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:03:14.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.9975ms
2025-05-26 00:03:17.285 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:03:17.286 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:03:17.289 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:03:17.289 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.2885ms
2025-05-26 00:03:20.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:03:20.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:03:20.285 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:03:20.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 5.3495ms
2025-05-26 00:03:23.285 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:03:23.287 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:03:23.289 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:03:23.289 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 5.0052ms
2025-05-26 00:03:26.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:03:26.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:03:26.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:03:26.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.482ms
2025-05-26 00:03:29.280 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:03:29.280 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:03:29.281 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:03:29.281 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.7152ms
2025-05-26 00:03:32.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:03:32.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:03:32.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:03:32.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.9917ms
2025-05-26 00:03:35.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:03:35.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:03:35.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:03:35.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.3826ms
2025-05-26 00:03:36.840 +02:00 [DBG] Server nomll7y29f004:1590:408fe5e8 heartbeat successfully sent
2025-05-26 00:03:38.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:03:38.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:03:38.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:03:38.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.4349ms
2025-05-26 00:03:41.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:03:41.282 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:03:41.282 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:03:41.283 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.0169ms
2025-05-26 00:03:44.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:03:44.286 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:03:44.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:03:44.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.4267ms
2025-05-26 00:03:47.285 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:03:47.286 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:03:47.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:03:47.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.0431ms
2025-05-26 00:03:50.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:03:50.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:03:50.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:03:50.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.1675ms
2025-05-26 00:03:53.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:03:53.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:03:53.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:03:53.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.7687ms
2025-05-26 00:03:56.279 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:03:56.279 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:03:56.280 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:03:56.280 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.6525ms
2025-05-26 00:03:59.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:03:59.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:03:59.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:03:59.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.6006ms
2025-05-26 00:04:02.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:04:02.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:04:02.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:04:02.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.2971ms
2025-05-26 00:04:05.288 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:04:05.289 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:04:05.289 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:04:05.290 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.4173ms
2025-05-26 00:04:06.841 +02:00 [DBG] Server nomll7y29f004:1590:408fe5e8 heartbeat successfully sent
2025-05-26 00:04:08.287 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:04:08.288 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:04:08.289 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:04:08.290 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.1283ms
2025-05-26 00:04:11.285 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:04:11.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:04:11.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:04:11.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.592ms
2025-05-26 00:04:14.289 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:04:14.291 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:04:14.292 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:04:14.292 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.4252ms
2025-05-26 00:04:17.288 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:04:17.289 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:04:17.291 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:04:17.292 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.8819ms
2025-05-26 00:04:20.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:04:20.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:04:20.285 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:04:20.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.8912ms
2025-05-26 00:04:23.281 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:04:23.281 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:04:23.282 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:04:23.282 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.8643ms
2025-05-26 00:04:26.281 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:04:26.282 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:04:26.283 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:04:26.284 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.3377ms
2025-05-26 00:04:29.281 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:04:29.282 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:04:29.283 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:04:29.283 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.3199ms
2025-05-26 00:04:32.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:04:32.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:04:32.289 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:04:32.293 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 10.2077ms
2025-05-26 00:04:35.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:04:35.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:04:35.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:04:35.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.166ms
2025-05-26 00:04:36.842 +02:00 [DBG] Server nomll7y29f004:1590:408fe5e8 heartbeat successfully sent
2025-05-26 00:04:38.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:04:38.282 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:04:38.282 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:04:38.283 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.7143ms
2025-05-26 00:04:41.278 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:04:41.278 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:04:41.279 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:04:41.279 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.323ms
2025-05-26 00:04:44.278 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:04:44.279 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:04:44.280 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:04:44.280 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.1347ms
2025-05-26 00:04:47.280 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:04:47.281 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:04:47.283 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:04:47.283 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.2462ms
2025-05-26 00:04:50.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:04:50.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:04:50.285 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:04:50.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.9027ms
2025-05-26 00:04:53.281 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:04:53.281 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:04:53.282 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:04:53.282 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.8334ms
2025-05-26 00:04:56.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:04:56.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:04:56.285 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:04:56.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.1946ms
2025-05-26 00:04:59.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:04:59.286 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:04:59.289 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:04:59.290 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 5.215ms
2025-05-26 00:05:02.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:05:02.286 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:05:02.288 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:05:02.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.6126ms
2025-05-26 00:05:05.281 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:05:05.281 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:05:05.282 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:05:05.282 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.0938ms
2025-05-26 00:05:06.844 +02:00 [DBG] Server nomll7y29f004:1590:408fe5e8 heartbeat successfully sent
2025-05-26 00:05:08.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:05:08.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:05:08.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:05:08.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.7635ms
2025-05-26 00:05:11.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:05:11.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:05:11.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:05:11.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.016ms
2025-05-26 00:05:11.379 +02:00 [DBG] 1 recurring job(s) processed by scheduler.
2025-05-26 00:05:14.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:05:14.286 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:05:14.288 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:05:14.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.3274ms
2025-05-26 00:05:17.281 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:05:17.282 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:05:17.284 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:05:17.285 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.3832ms
2025-05-26 00:05:20.281 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:05:20.281 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:05:20.281 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:05:20.282 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.057ms
2025-05-26 00:05:23.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:05:23.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:05:23.285 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:05:23.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.0422ms
2025-05-26 00:05:26.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:05:26.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:05:26.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:05:26.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.2707ms
2025-05-26 00:05:29.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:05:29.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:05:29.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:05:29.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.3806ms
2025-05-26 00:05:32.298 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:05:32.299 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:05:32.300 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:05:32.300 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.8148ms
2025-05-26 00:05:35.279 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:05:35.279 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:05:35.280 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:05:35.280 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.7206ms
2025-05-26 00:05:36.846 +02:00 [DBG] Server nomll7y29f004:1590:408fe5e8 heartbeat successfully sent
2025-05-26 00:05:38.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:05:38.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:05:38.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:05:38.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.1977ms
2025-05-26 00:05:41.281 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:05:41.282 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:05:41.283 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:05:41.283 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.7015ms
2025-05-26 00:05:44.279 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:05:44.279 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:05:44.280 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:05:44.281 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.7257ms
2025-05-26 00:05:47.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:05:47.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:05:47.284 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:05:47.284 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.0567ms
2025-05-26 00:05:50.281 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:05:50.282 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:05:50.283 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:05:50.284 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.8451ms
2025-05-26 00:05:53.288 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:05:53.290 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:05:53.292 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:05:53.292 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.0136ms
2025-05-26 00:05:56.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:05:56.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:05:56.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:05:56.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.1495ms
2025-05-26 00:05:59.285 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:05:59.286 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:05:59.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:05:59.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.8876ms
2025-05-26 00:06:02.281 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:06:02.281 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:06:02.281 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:06:02.281 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.803ms
2025-05-26 00:06:05.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:06:05.286 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:06:05.288 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:06:05.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.3091ms
2025-05-26 00:06:06.849 +02:00 [DBG] Server nomll7y29f004:1590:408fe5e8 heartbeat successfully sent
2025-05-26 00:06:08.285 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:06:08.286 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:06:08.288 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:06:08.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.2771ms
2025-05-26 00:06:11.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:06:11.286 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:06:11.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:06:11.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.0765ms
2025-05-26 00:06:14.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:06:14.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:06:14.284 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:06:14.284 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.1765ms
2025-05-26 00:06:17.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:06:17.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:06:17.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:06:17.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.8851ms
2025-05-26 00:06:20.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:06:20.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:06:20.285 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:06:20.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.3563ms
2025-05-26 00:06:23.286 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:06:23.286 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:06:23.288 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:06:23.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.8701ms
2025-05-26 00:06:26.285 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:06:26.286 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:06:26.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:06:26.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.0702ms
2025-05-26 00:06:29.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:06:29.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:06:29.284 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:06:29.284 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.9775ms
2025-05-26 00:06:32.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:06:32.282 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:06:32.283 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:06:32.283 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.9528ms
2025-05-26 00:06:35.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:06:35.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:06:35.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:06:35.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.256ms
2025-05-26 00:06:36.851 +02:00 [DBG] Server nomll7y29f004:1590:408fe5e8 heartbeat successfully sent
2025-05-26 00:06:38.285 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:06:38.286 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:06:38.288 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:06:38.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.4194ms
2025-05-26 00:06:41.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:06:41.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:06:41.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:06:41.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.608ms
2025-05-26 00:06:44.280 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:06:44.280 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:06:44.281 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:06:44.281 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.9204ms
2025-05-26 00:06:47.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:06:47.287 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:06:47.288 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:06:47.289 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.382ms
2025-05-26 00:06:50.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:06:50.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:06:50.285 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:06:50.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 5.0035ms
2025-05-26 00:06:53.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:06:53.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:06:53.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:06:53.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.7837ms
2025-05-26 00:06:56.279 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:06:56.280 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:06:56.280 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:06:56.280 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.9201ms
2025-05-26 00:06:59.300 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:06:59.300 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:06:59.301 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:06:59.303 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.9152ms
2025-05-26 00:07:02.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:07:02.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:07:02.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:07:02.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.5612ms
2025-05-26 00:07:05.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:07:05.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:07:05.288 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:07:05.289 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 6.6923ms
2025-05-26 00:07:06.853 +02:00 [DBG] Server nomll7y29f004:1590:408fe5e8 heartbeat successfully sent
2025-05-26 00:07:08.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:07:08.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:07:08.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:07:08.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.1905ms
2025-05-26 00:07:11.280 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:07:11.280 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:07:11.281 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:07:11.281 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.0275ms
2025-05-26 00:07:14.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:07:14.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:07:14.285 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:07:14.285 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.8744ms
2025-05-26 00:07:17.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:07:17.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:07:17.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:07:17.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.2691ms
2025-05-26 00:07:20.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:07:20.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:07:20.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:07:20.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.4903ms
2025-05-26 00:07:23.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:07:23.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:07:23.284 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:07:23.285 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.3584ms
2025-05-26 00:07:26.280 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:07:26.281 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:07:26.281 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:07:26.281 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.1093ms
2025-05-26 00:07:29.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:07:29.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:07:29.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:07:29.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.9413ms
2025-05-26 00:07:32.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:07:32.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:07:32.285 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:07:32.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.1181ms
2025-05-26 00:07:35.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:07:35.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:07:35.285 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:07:35.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.1716ms
2025-05-26 00:07:36.857 +02:00 [DBG] Server nomll7y29f004:1590:408fe5e8 heartbeat successfully sent
2025-05-26 00:07:38.281 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:07:38.281 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:07:38.282 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:07:38.282 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.9532ms
2025-05-26 00:07:41.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:07:41.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:07:41.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:07:41.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.7076ms
2025-05-26 00:07:44.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:07:44.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:07:44.285 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:07:44.285 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.8878ms
2025-05-26 00:07:47.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:07:47.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:07:47.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:07:47.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.2376ms
2025-05-26 00:07:50.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:07:50.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:07:50.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:07:50.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.2585ms
2025-05-26 00:07:53.280 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:07:53.281 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:07:53.281 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:07:53.281 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.8729ms
2025-05-26 00:07:56.287 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:07:56.288 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:07:56.289 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:07:56.289 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.6475ms
2025-05-26 00:07:59.280 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:07:59.282 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:07:59.284 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:07:59.285 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 5.1923ms
2025-05-26 00:08:02.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:08:02.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:08:02.285 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:08:02.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.2963ms
2025-05-26 00:08:05.280 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:08:05.280 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:08:05.281 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:08:05.281 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.0485ms
2025-05-26 00:08:06.859 +02:00 [DBG] Server nomll7y29f004:1590:408fe5e8 heartbeat successfully sent
2025-05-26 00:08:08.280 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:08:08.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:08:08.284 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:08:08.285 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.5377ms
2025-05-26 00:08:11.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:08:11.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:08:11.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:08:11.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.2012ms
2025-05-26 00:08:14.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:08:14.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:08:14.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:08:14.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.5322ms
2025-05-26 00:08:17.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:08:17.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:08:17.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:08:17.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.5814ms
2025-05-26 00:08:20.279 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:08:20.280 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:08:20.280 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:08:20.280 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.9935ms
2025-05-26 00:08:23.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:08:23.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:08:23.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:08:23.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.1117ms
2025-05-26 00:08:26.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:08:26.285 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:08:26.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:08:26.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.1858ms
2025-05-26 00:08:29.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:08:29.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:08:29.285 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:08:29.285 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.9238ms
2025-05-26 00:08:32.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:08:32.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:08:32.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:08:32.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.9243ms
2025-05-26 00:08:35.281 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:08:35.281 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:08:35.281 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:08:35.281 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.6844ms
2025-05-26 00:08:36.860 +02:00 [DBG] Server nomll7y29f004:1590:408fe5e8 heartbeat successfully sent
2025-05-26 00:08:38.300 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:08:38.301 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:08:38.303 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:08:38.304 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.345ms
2025-05-26 00:08:41.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:08:41.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:08:41.285 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:08:41.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.1555ms
2025-05-26 00:08:44.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:08:44.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:08:44.285 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:08:44.285 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.8026ms
2025-05-26 00:08:47.280 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:08:47.280 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:08:47.281 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:08:47.281 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.9779ms
2025-05-26 00:08:50.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:08:50.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:08:50.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:08:50.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.2165ms
2025-05-26 00:08:53.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:08:53.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:08:53.285 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:08:53.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.0421ms
2025-05-26 00:08:56.278 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:08:56.279 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:08:56.280 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:08:56.281 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.9058ms
2025-05-26 00:08:59.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:08:59.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:08:59.284 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:08:59.285 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.9837ms
2025-05-26 00:09:02.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:09:02.282 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:09:02.283 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:09:02.283 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.7706ms
2025-05-26 00:09:05.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:09:05.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:09:05.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:09:05.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.7048ms
2025-05-26 00:09:06.862 +02:00 [DBG] Server nomll7y29f004:1590:408fe5e8 heartbeat successfully sent
2025-05-26 00:09:08.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:09:08.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:09:08.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:09:08.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 5.0771ms
2025-05-26 00:09:11.281 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:09:11.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:09:11.284 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:09:11.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.545ms
2025-05-26 00:09:14.280 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:09:14.281 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:09:14.282 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:09:14.282 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.5033ms
2025-05-26 00:09:17.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:09:17.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:09:17.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:09:17.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.1168ms
2025-05-26 00:09:20.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:09:20.287 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:09:20.289 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:09:20.290 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 6.2664ms
2025-05-26 00:09:23.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:09:23.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:09:23.284 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:09:23.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 5.0011ms
2025-05-26 00:09:26.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:09:26.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:09:26.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:09:26.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.8986ms
2025-05-26 00:09:29.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:09:29.282 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:09:29.283 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:09:29.283 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.9514ms
2025-05-26 00:09:32.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:09:32.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:09:32.284 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:09:32.285 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.0435ms
2025-05-26 00:09:35.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:09:35.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:09:35.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:09:35.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.0314ms
2025-05-26 00:09:36.864 +02:00 [DBG] Server nomll7y29f004:1590:408fe5e8 heartbeat successfully sent
2025-05-26 00:09:38.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:09:38.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:09:38.284 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:09:38.285 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.174ms
2025-05-26 00:09:41.280 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:09:41.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:09:41.286 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:09:41.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 6.655ms
2025-05-26 00:09:44.279 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:09:44.279 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:09:44.279 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:09:44.280 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.7536ms
2025-05-26 00:09:47.280 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:09:47.282 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:09:47.284 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:09:47.286 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.7255ms
2025-05-26 00:09:50.284 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:09:50.286 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:09:50.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:09:50.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.8036ms
2025-05-26 00:09:53.279 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:09:53.279 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:09:53.280 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:09:53.280 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.3836ms
2025-05-26 00:09:56.281 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:09:56.281 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:09:56.282 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:09:56.282 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.1543ms
2025-05-26 00:09:59.281 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:09:59.282 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:09:59.283 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:09:59.284 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.0674ms
2025-05-26 00:10:02.283 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:10:02.284 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:10:02.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:10:02.287 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.9557ms
2025-05-26 00:10:05.281 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:10:05.281 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:10:05.283 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:10:05.283 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.4496ms
2025-05-26 00:10:06.865 +02:00 [DBG] Server nomll7y29f004:1590:408fe5e8 heartbeat successfully sent
2025-05-26 00:10:08.282 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:10:08.283 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:10:08.287 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:10:08.288 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 5.623ms
2025-05-26 00:10:11.281 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:10:11.281 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:10:11.282 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:10:11.282 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.8736ms
2025-05-26 00:10:11.401 +02:00 [DBG] 2 recurring job(s) processed by scheduler.
2025-05-26 00:10:11.458 +02:00 [INF] Start processing HTTP request GET https://www.finn.no/api/search-qf?searchkey=SEARCH_ID_REALESTATE_HOMES&vertical=realestate&page=1
2025-05-26 00:10:11.481 +02:00 [INF] Sending HTTP request GET https://www.finn.no/api/search-qf?searchkey=SEARCH_ID_REALESTATE_HOMES&vertical=realestate&page=1
2025-05-26 00:10:11.820 +02:00 [INF] Received HTTP response headers after 335.8862ms - 200
2025-05-26 00:10:11.821 +02:00 [INF] End processing HTTP request after 366.1425ms - 200
2025-05-26 00:10:12.447 +02:00 [INF] Executed DbCommand (68ms) [Parameters=[@__apiIds_0='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
SELECT r.finn_ad_id
FROM real_estate_listing AS r
WHERE r.finn_ad_id = ANY (@__apiIds_0)
2025-05-26 00:10:12.447 +02:00 [INF] No new listings to add to the database.
2025-05-26 00:10:14.318 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:10:14.323 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:10:14.324 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:10:14.324 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 5.9557ms
2025-05-26 00:10:17.286 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:10:17.287 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:10:17.290 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:10:17.290 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.1551ms
2025-05-26 00:10:20.289 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:10:20.290 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:10:20.291 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:10:20.293 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.0619ms
2025-05-26 00:13:23.821 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:13:23.822 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:13:23.823 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:13:23.823 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.2923ms
2025-05-26 00:13:26.820 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:13:26.820 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:13:26.823 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:13:26.824 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.9807ms
2025-05-26 00:13:30.725 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:13:30.726 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:13:30.727 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:13:30.728 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.4584ms
2025-05-26 00:13:33.729 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:13:33.730 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:13:33.732 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:13:33.733 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.6971ms
2025-05-26 00:13:36.730 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:13:36.731 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:13:36.732 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:13:36.734 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.7982ms
2025-05-26 00:16:19.337 +02:00 [DBG] Server nomll7y29f004:1590:408fe5e8 heartbeat successfully sent
2025-05-26 00:16:20.761 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:16:20.761 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:16:20.761 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:16:20.761 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.7177ms
2025-05-26 00:16:23.754 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:16:23.755 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:16:23.757 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:16:23.758 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.628ms
2025-05-26 00:16:23.877 +02:00 [DBG] 1 recurring job(s) processed by scheduler.
2025-05-26 00:23:35.115 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:23:35.116 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:23:35.117 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:23:35.117 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.8522ms
2025-05-26 00:23:38.122 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:23:38.123 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:23:38.124 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:23:38.124 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.54ms
2025-05-26 00:23:41.117 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:23:41.117 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:23:41.118 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:23:41.118 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.0559ms
2025-05-26 00:38:56.630 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:38:56.631 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:38:56.633 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:38:56.633 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.7549ms
2025-05-26 00:38:59.589 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-26 00:38:59.590 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-26 00:38:59.591 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-26 00:38:59.592 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.2279ms
2025-05-26 00:40:02.357 +02:00 [DBG] 2 recurring job(s) processed by scheduler.
2025-05-26 00:40:02.450 +02:00 [INF] Start processing HTTP request GET https://www.finn.no/api/search-qf?searchkey=SEARCH_ID_REALESTATE_HOMES&vertical=realestate&page=1
2025-05-26 00:40:02.450 +02:00 [INF] Sending HTTP request GET https://www.finn.no/api/search-qf?searchkey=SEARCH_ID_REALESTATE_HOMES&vertical=realestate&page=1
