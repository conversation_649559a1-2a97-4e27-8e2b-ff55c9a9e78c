{"AllowedHosts": "*", "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Hangfire": "Information"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "Hangfire": "Information"}}}, "ConnectionStrings": {"DefaultConnection": "User Id=postgres.pgrwknouyfhzhmwvzagh;Password=*************;Server=aws-0-eu-central-1.pooler.supabase.com;Port=5432;Database=postgres;"}}