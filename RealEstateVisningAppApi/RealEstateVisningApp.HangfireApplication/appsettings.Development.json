{
  "AppSettingsEnvironment": "Development",
  "AllowedHosts": "*",
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Hangfire": "Information"
    }
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning",
        "Hangfire": "Information"
      }
    }
  },
  "Hangfire": {
    "UseInMemoryStorage": true,
    "BackgroundJobs": {
      "UpdateNewestRealEstates": {
        "IsEnabled": true,
        "Cron": "*/10 7-23 * * *"
      },
      "UpdateAllRealEstates": {
        "IsEnabled": false,
        "Cron": "0 6 * * *"
      }
    },
    "RemoveAllRecurringJobs": false
  },
  "Supabase": {
    "Url": "https://pgrwknouyfhzhmwvzagh.supabase.co",
    "AnonKey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBncndrbm91eWZoemhtd3Z6YWdoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjI4NzA2MDQsImV4cCI6MjAzODQ0NjYwNH0.zs6rnaNyGZpfCloDLgVH6UHYnXzS3DDGoLN8SQrx9sU"
  },
    "ConnectionStrings": {
    "DefaultConnection": "User Id=postgres.pgrwknouyfhzhmwvzagh;Password=*************;Server=aws-0-eu-central-1.pooler.supabase.com;Port=5432;Database=postgres;"
  },
}
