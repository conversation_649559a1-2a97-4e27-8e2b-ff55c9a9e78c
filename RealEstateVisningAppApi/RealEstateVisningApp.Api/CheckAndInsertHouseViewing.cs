using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Npgsql;

namespace RealEstateVisningAppApi;
public class House
{
    public Guid Id { get; set; }
}

public class HouseViewing
{
    public long Id { get; set; }
    public Guid RealEstateId { get; set; }
    public Guid OwnerUserId { get; set; }
    public List<AccessList> AccessList { get; set; }
    public List<Image> Images { get; set; }
    public DateTime CreatedAt { get; set; }
    // Add other relevant properties
    public RealEstate RealEstate { get; set; } // Add this property to hold the related RealEstate
}

public class Image
{
    public string Id { get; set; }
    public string Comment { get; set; }
    public string Filename { get; set; }
    public string ImageUrl { get; set; }
}


public class AccessList
{
    public string Email { get; set; }
}

public class RealEstate
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public long? FinnId { get; set; }
    public DateTime? LastUpdatedAt { get; set; }
    public DateTime? Timestamp { get; set; }
    public string Location { get; set; }
    public string SearchLocation { get; set; }
    public double? TimestampFinn { get; set; }
    public long? TotalPriceInitial { get; set; }
    public long? TotalPriceCurrent { get; set; }
    public long? PriceSuggestionInitial { get; set; }
    public long? PriceSuggestionCurrent { get; set; }
    public long? PriceSharedCost { get; set; }
    public long? PriceSuggestionChangeAmount { get; set; }
    public long? PriceSuggestionChangePercentage { get; set; }
    public long? Tax { get; set; }
    public long? TotalPriceChangeAmount { get; set; }
    public long? TotalPriceChangePercentage { get; set; }
    public FinnData FinnData { get; set; }
}


public class AreaPlot
    {
        public int Size { get; set; }
        public string Unit { get; set; }
        public string Description { get; set; }
    }

    public class AreaRange
    {
        public string Unit { get; set; }
        public int SizeTo { get; set; }
        public int SizeFrom { get; set; }
        public string Description { get; set; }
    }

    public class Coordinates
    {
        public double Lat { get; set; }
        public double Lon { get; set; }
    }

    public class FinnDataImage
    {
        public string Url { get; set; }
        public string Path { get; set; }
        public int Width { get; set; }
        public int Height { get; set; }
        public double AspectRatio { get; set; }
    }

    public class Logo
    {
        public string Url { get; set; }
        public string Path { get; set; }
    }

    public class PriceSharedCost
    {
        public int Amount { get; set; }
        public string CurrencyCode { get; set; }
    }

    public class PriceSuggestion
    {
        public int Amount { get; set; }
        public string CurrencyCode { get; set; }
    }

    public class PriceTotal
    {
        public int Amount { get; set; }
        public string CurrencyCode { get; set; }
    }

    public class FinnData
    {
        public int AdId { get; set; }
        public Logo Logo { get; set; }
        public string Type { get; set; }
        public List<string> Flags { get; set; }
        public FinnDataImage Image { get; set; }
        public string AdLink { get; set; }
        public int AdType { get; set; }
        public List<object> Labels { get; set; }
        public string Heading { get; set; }
        public List<string> Styling { get; set; }
        public AreaPlot AreaPlot { get; set; }
        public string Location { get; set; }
        public AreaRange AreaRange { get; set; }
        public List<string> ImageUrls { get; set; }
        public long Timestamp { get; set; }
        public PriceTotal PriceTotal { get; set; }
        public Coordinates Coordinates { get; set; }
        public List<object> ViewingTimes { get; set; }
        public object BedroomsRange { get; set; }
        public string LocalAreaName { get; set; }
        public string MainSearchKey { get; set; }
        public object PriceRangeTotal { get; set; }
        public PriceSharedCost PriceSharedCost { get; set; }
        public PriceSuggestion PriceSuggestion { get; set; }
        public int? NumberOfBedrooms { get; set; }
        public string OrganisationName { get; set; }
        public string OwnerTypeDescription { get; set; }
        public object PriceRangeSuggestion { get; set; }
        public string PropertyTypeDescription { get; set; }
    }

public class JsonbDynamicTypeHandler : SqlMapper.TypeHandler<dynamic>
{
    public override void SetValue(IDbDataParameter parameter, dynamic value)
    {
        parameter.Value = JsonConvert.SerializeObject(value);
        parameter.DbType = DbType.String;
    }

    public override dynamic Parse(object value)
    {
        return JsonConvert.DeserializeObject<dynamic>(value.ToString());
    }
}
public class JsonbListTypeHandler<T> : SqlMapper.TypeHandler<List<T>>
{
    public override void SetValue(IDbDataParameter parameter, List<T> value)
    {
        parameter.Value = JsonConvert.SerializeObject(value);
        parameter.DbType = DbType.String;
    }

    public override List<T> Parse(object value)
    {
        return JsonConvert.DeserializeObject<List<T>>(value.ToString());
    }
}

public class JsonbTypeHandler<T> : SqlMapper.TypeHandler<T>
{
    public override void SetValue(IDbDataParameter parameter, T value)
    {
        parameter.Value = JsonConvert.SerializeObject(value);
        parameter.DbType = DbType.String;
    }

    public override T Parse(object value)
    {
        return JsonConvert.DeserializeObject<T>(value.ToString());
    }
}

    
public class CheckAndInsertHouseViewing
{
    private static readonly string ConnectionString = Environment.GetEnvironmentVariable("PostgresConnectionString");

    private readonly ILogger<CheckAndInsertHouseViewing> _logger;
    
    public CheckAndInsertHouseViewing(ILogger<CheckAndInsertHouseViewing> logger)
    {
        _logger = logger;
    }

    [Function("CheckAndInsertHouseViewing")]
    public async Task<IActionResult> RunAsync([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = null)] HttpRequest req)
    {
        SqlMapper.AddTypeHandler(new JsonbListTypeHandler<Image>());
        SqlMapper.AddTypeHandler(new JsonbListTypeHandler<AccessList>());
        // SqlMapper.AddTypeHandler(new JsonbDynamicTypeHandler());
        SqlMapper.AddTypeHandler(new JsonbTypeHandler<FinnData>());
        string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
        House home = JsonConvert.DeserializeObject<House>(requestBody);
        string userId = req.Headers["X-User-Id"];
        string userEmail = req.Headers["X-User-Email"];

        if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(userEmail))
        {
            return new BadRequestObjectResult(new { error = "User is not authenticated" });
        }
        
        using (IDbConnection db = new NpgsqlConnection(ConnectionString))
        {
            try
            {
                var query = @"
                    SELECT hv.*, re.*,  re.""FinnData""::jsonb as ""FinnData""
                    FROM ""HouseViewing"" hv
                    JOIN ""RealEstate"" re ON hv.""RealEstateId"" = re.""Id""
                    WHERE hv.""RealEstateId"" = @RealEstateId
                    AND (hv.""OwnerUserId"" = @OwnerUserId OR hv.""AccessList"" @> to_jsonb(@UserEmail))";

                var parameters = new
                {
                    RealEstateId = home.Id,
                    OwnerUserId = Guid.Parse(userId),
                    UserEmail = new[] { userEmail } // Make sure this is a string array for JSONB comparison
                };
                
                var existingHouseViewings = await db.QueryAsync<HouseViewing, RealEstate, HouseViewing>(
                    query,
                    (houseViewing, realEstate) =>
                    {
                        houseViewing.RealEstateId = realEstate.Id;
                        // Perform any additional mappings if necessary
                        // houseViewing.RealEstate.FinnData = JsonConvert.DeserializeObject<FinnData>(realEstate.FinnData.ToString());
                        houseViewing.RealEstate = realEstate;
                        return houseViewing;
                    },
                    parameters,
                    splitOn: "Id"
                );
                var existingHouseViewing = existingHouseViewings.FirstOrDefault();
                
                if (existingHouseViewing != null)
                {
                    _logger.LogInformation("HouseViewing record already exists");
                    return new OkObjectResult(existingHouseViewing);
                }
                else
                {
                    var insertQuery = @"
                        INSERT INTO ""HouseViewing"" (""OwnerUserId"", ""RealEstateId"", ""AccessList"", ""Images"")
                         VALUES (@OwnerUserId, @RealEstateId, CAST(@AccessList AS jsonb), CAST(@Images AS jsonb))";
                    
                    var insertParameters = new
                    {
                        OwnerUserId = Guid.Parse(userId),
                        RealEstateId = home.Id,
                        AccessList = JsonConvert.SerializeObject(new List<AccessList>()), // Serialize to empty JSON array
                        Images = JsonConvert.SerializeObject(new List<Image>()) 
                    };
                    
                    var t = await db.ExecuteAsync(insertQuery, insertParameters);

                    var existingHouseViewings2 = await db.QueryAsync<HouseViewing, RealEstate, HouseViewing>(
                        query,
                        (houseViewing, realEstate) =>
                        {
                            houseViewing.RealEstateId = realEstate.Id;
                            houseViewing.RealEstate = realEstate;
                            return houseViewing;
                        },
                        parameters,
                        splitOn: "Id"
                    );
                    var house = existingHouseViewings2.FirstOrDefault();
                    
                  
                    
                    _logger.LogInformation("HouseViewing record created");
                    return new OkObjectResult(house);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error checking or inserting HouseViewing record: {0}", ex.Message);
                return new ObjectResult(new { error = ex.Message }) { StatusCode = 500 };
            }
        }
    }
}