using HtmlAgilityPack;
using Moq;
using RealEstateVisningApp.BusinessLayer.Finn.CrawlFinn;
using RealEstateVisningApp.BusinessLayer.Finn.CrawlFinn.FinnWebScrapper;

namespace RealEstateVisningApp.BusinessLayer.Tests;

[TestFixture]
public class FinnWebScrapingServiceTests
{
    private FinnWebScrapingService _finnWebScrapingService;
    private Mock<IHtmlFetchService> _mockHtmlFetchService;
    private Mock<IMatrikkelInfoExtractorService> _mockMatrikkelInfoExtractorService;

    [SetUp]
    public void Setup()
    {
        _mockHtmlFetchService = new Mock<IHtmlFetchService>();
        _mockMatrikkelInfoExtractorService = new Mock<IMatrikkelInfoExtractorService>();
        _finnWebScrapingService = new FinnWebScrapingService(
            _mockHtmlFetchService.Object,
            _mockMatrikkelInfoExtractorService.Object);
    }

    [Test]
    public async Task adasd()
    {
        var finnWebScrapingService = new FinnWebScrapingService(new HtmlFetchService(), new MatrikkelInfoExtractorService());

        var url = "https://www.finn.no/realestate/homes/ad.html?finnkode=365942151";
        var result = await finnWebScrapingService.ScrapFinnAdData(url);
    }

    // [Test]
    // public async Task ScrapFinnAdData_WithRealUrl_ReturnsValidData()
    // {
    //     // Arrange
    //     var realUrl = "https://www.finn.no/realestate/homes/ad.html?finnkode=365942151"; // Replace with a real Finn.no ad URL
    //     var expectedHtml = "<html>...actual HTML content...</html>";
    //     var expectedMatrikkelInfo = new MatrikkelInfo
    //     {
    //         Kommunenr = "0301",
    //         Gardsnr = "123",
    //         Bruksnr = "456"
    //         // Set other properties as needed
    //     };
    //
    //     _mockHtmlFetchService.Setup(x => x.FetchHtmlAsync(realUrl))
    //         .ReturnsAsync(expectedHtml);
    //
    //     _mockMatrikkelInfoExtractorService.Setup(x => x.ExtractMatrikkelInfo(It.IsAny<HtmlDocument>()))
    //         .Returns(expectedMatrikkelInfo);
    //
    //     // Act
    //     var result = await _finnWebScrapingService.ScrapFinnAdData(realUrl);
    //
    //     // Assert
    //     Assert.IsNotNull(result);
    //     Assert.IsNotNull(result.MatrikkelInfo);
    //     Assert.AreEqual(expectedMatrikkelInfo.Kommunenr, result.MatrikkelInfo.Kommunenr);
    //     Assert.AreEqual(expectedMatrikkelInfo.Gardsnr, result.MatrikkelInfo.Gardsnr);
    //     Assert.AreEqual(expectedMatrikkelInfo.Bruksnr, result.MatrikkelInfo.Bruksnr);
    //
    //     _mockHtmlFetchService.Verify(x => x.FetchHtmlAsync(realUrl), Times.Once);
    //     _mockMatrikkelInfoExtractorService.Verify(x => x.ExtractMatrikkelInfo(It.IsAny<HtmlDocument>()), Times.Once);
    // }
    //
    // [Test]
    // public async Task ScrapFinnAdData_WithInvalidUrl_ThrowsException()
    // {
    //     // Arrange
    //     var invalidUrl = "https://www.finn.no/invalid-url";
    //     _mockHtmlFetchService.Setup(x => x.FetchHtmlAsync(invalidUrl))
    //         .ThrowsAsync(new HttpRequestException("Not Found"));
    //
    //     // Act & Assert
    //     var ex = Assert.ThrowsAsync<HttpRequestException>(async () => 
    //         await _finnWebScrapingService.ScrapFinnAdData(invalidUrl));
    //     Assert.That(ex.Message, Is.EqualTo("Not Found"));
    // }
    //
    // [Test]
    // public async Task ScrapFinnAdData_WithMissingMatrikkelInfo_ReturnsEmptyMatrikkelInfo()
    // {
    //     // Arrange
    //     var url = "https://www.finn.no/realestate/homes/ad.html?finnkode=987654321"; // Replace with a real Finn.no ad URL
    //     var htmlWithoutMatrikkelInfo = "<html><body>No Matrikkel Info Here</body></html>";
    //
    //     _mockHtmlFetchService.Setup(x => x.FetchHtmlAsync(url))
    //         .ReturnsAsync(htmlWithoutMatrikkelInfo);
    //
    //     _mockMatrikkelInfoExtractorService.Setup(x => x.ExtractMatrikkelInfo(It.IsAny<HtmlDocument>()))
    //         .Returns(new MatrikkelInfo());
    //
    //     // Act
    //     var result = await _finnWebScrapingService.ScrapFinnAdData(url);
    //
    //     // Assert
    //     Assert.IsNotNull(result);
    //     Assert.IsNotNull(result.MatrikkelInfo);
    //     Assert.IsNull(result.MatrikkelInfo.Kommunenr);
    //     Assert.IsNull(result.MatrikkelInfo.Gardsnr);
    //     Assert.IsNull(result.MatrikkelInfo.Bruksnr);
    // }
}